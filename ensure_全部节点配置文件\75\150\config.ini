# 分布式文件处理系统配置文件

# 数据库模块配置：监听指定目录，解析不同格式文件内容，结构化存储到SQLite数据库
[database]
path = database.db
watch_directories = /home/<USER>/SourceData;/home/<USER>/dataShare
database_enable = true
max_connections = 10
connection_timeout = 30
retry_attempts = 3
retry_delay = 1.0
wal_mode = true
synchronous = NORMAL
cache_size = -64000
temp_store = MEMORY
mmap_size = 268435456
journal_mode = WAL
foreign_keys = true
auto_vacuum = INCREMENTAL
busy_timeout = 30000
insert_sample_data = true

# 文件发布模块配置：监控文件系统事件，通过ZeroMQ发布新文件到指定端点
[file_publish]
watch_paths = /home/<USER>/SourceData;/home/<USER>/dataShare
zmq_publish_addr = tcp://0.0.0.0:13236
topics = leo_data
file_types = *.json
file_publish_enable = true

# API服务模块配置：基于FastAPI提供RESTful接口，支持多客户端查询，结果通过ZeroMQ发布
[api]
host = 0.0.0.0
port = 13231
json_zmq_bind_addr = tcp://0.0.0.0:13239
topics = api_data
api_enable = true

# 订阅服务模块配置：订阅多个ZeroMQ主题，接收并持久化文件到本地
[subscribe]
save_directory = /home/<USER>/dataShare/api_result
zmq_connect_addrs=*********,**********,*********,*********,*********
topics = leo_data,api_data
subscribe_enable = true



# 日志配置
[logging]
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO
# 日志格式（注意：INI文件中%需要转义为%%）
format = [%%(asctime)s] [%%(name)s] %%(message)s
# 日志格式（注意：INI文件中%需要转义为%%）
date_format = %%Y-%%m-%%d %%H:%%M:%%S
# log日志保存路径
log_path = /home/<USER>/dataShare.log

# 分布式数据收集和存储模块配置：三线程系统实现数据收集、网络订阅和数据处理
[store]
# 数据库配置
db_path = store.db
max_connections = 10

# 目录监控配置（生产者线程1）
watch_directories = ../111
zmq_publish_addr = tcp://0.0.0.0:13232

# 网络订阅配置（生产者线程2）
cluster_nodes = 127.0.0.1
subscribe_topics = original_data,feature_data

# 数据处理配置（消费者线程）
max_workers = 4
queue_maxsize = 1000

# 服务启用状态
service_enabled = true

# 搜索服务模块配置：基于FastAPI提供RESTful接口，移除ZeroMQ，结果保存到本地文件
[search]
# 服务配置
host = 0.0.0.0
port = 8001

# 文件保存配置
save_directory = ./search_results
file_prefix = search

# 文件命名规则配置
# 格式: {file_prefix}_{query_type}_{device_id}_{timestamp}_{current_time}.json
# 或: {file_prefix}_{query_type}_{style}_{current_time}.json
# 或: {file_prefix}_{query_type}_{current_time}.json

# 服务启用状态
search_enable = true