import os
import select
import struct
import socket
import time
import logging
from datetime import datetime
from collections import defaultdict
from fcntl import ioctl

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("udp_receiver.log"),
        logging.StreamHandler()
    ]
)

# 常量定义
UDP_PORT = 34572
MULTICAST_ADDR = "230.225.0.115"  # 组播地址
BUFFER_SIZE = 31720
IMAGE_DIR = "./SourceData"
REFERENCE_TIMESTAMP = 1672502400000  # 2023-01-01 00:00:00 UTC的毫秒时间戳

class FileIndexer:
    def __init__(self):
        self.image_index = defaultdict(list)  # {device_id: [(timestamp, filename), ...]}
        self.json_index = defaultdict(list)   # {device_id: [(timestamp, filename), ...]}
    
    def build_index(self, directory):
        """构建文件索引"""
        logging.info(f"开始构建文件索引，目录: {directory}")
        start_time = time.time()
        file_count = 0
        
        # 确保目录存在
        if not os.path.exists(directory):
            os.makedirs(directory)
            logging.info(f"创建目录: {directory}")
        
        for filename in os.listdir(directory):
            if not (filename.endswith(".jpg") or filename.endswith(".json")):
                continue
                
            parts = filename.split('_')
            if len(parts) < 3:
                continue
                
            try:
                device_id = int(parts[0])
                timestamp_str = parts[2].split('.')[0]  # 去掉扩展名
                timestamp = int(timestamp_str)
                
                if filename.endswith(".jpg"):
                    self.image_index[device_id].append((timestamp, filename))
                elif filename.endswith(".json"):
                    self.json_index[device_id].append((timestamp, filename))
                
                file_count += 1
            except (ValueError, IndexError):
                continue
        
        # 按时间戳排序每个设备的文件列表
        for device_id in self.image_index:
            self.image_index[device_id].sort(key=lambda x: x[0])
        for device_id in self.json_index:
            self.json_index[device_id].sort(key=lambda x: x[0])
        
        elapsed = time.time() - start_time
        logging.info(f"索引构建完成: 处理 {file_count} 个文件, 耗时 {elapsed:.3f} 秒")
        logging.info(f"设备统计: {len(self.image_index)} 个设备有图片, {len(self.json_index)} 个设备有JSON")
    
    def find_closest_file(self, device_id, target_timestamp, file_type):
        """在索引中查找最接近目标时间戳的文件"""
        index = self.image_index if file_type == "image" else self.json_index
        
        if device_id not in index or not index[device_id]:
            return None
        
        # 使用二分查找最接近的文件
        files = index[device_id]
        low, high = 0, len(files) - 1
        
        # 特殊情况处理
        if target_timestamp <= files[0][0]:
            return files[0][1]
        if target_timestamp >= files[-1][0]:
            return files[-1][1]
        
        # 二分查找
        while low <= high:
            mid = (low + high) // 2
            mid_ts = files[mid][0]
            
            if mid_ts < target_timestamp:
                low = mid + 1
            elif mid_ts > target_timestamp:
                high = mid - 1
            else:
                return files[mid][1]
        
        # 比较相邻元素的接近程度
        if low > 0 and low < len(files):
            prev_diff = abs(files[low-1][0] - target_timestamp)
            next_diff = abs(files[low][0] - target_timestamp)
            return files[low-1][1] if prev_diff < next_diff else files[low][1]
        
        return files[0][1]  # 默认返回第一个
    
    def update_index_after_rename(self, device_id, old_filename, new_filename, file_type):
        """重命名后更新索引"""
        index = self.image_index if file_type == "image" else self.json_index
        
        if device_id not in index:
            return
        
        # 解析新文件名的时间戳
        try:
            parts = new_filename.split('_')
            new_timestamp = int(parts[2].split('.')[0])
        except (IndexError, ValueError):
            logging.warning(f"无法解析新文件名的时间戳: {new_filename}")
            return
        
        # 查找并替换旧条目
        for i, (ts, fname) in enumerate(index[device_id]):
            if fname == old_filename:
                index[device_id][i] = (new_timestamp, new_filename)
                index[device_id].sort(key=lambda x: x[0])  # 保持排序
                return
        
        # 如果没找到旧条目，添加新条目
        index[device_id].append((new_timestamp, new_filename))
        index[device_id].sort(key=lambda x: x[0])

def ensure_directory_exists(directory):
    """确保目录存在，不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logging.info(f"创建目录: {directory}")
        return True
    return False

def parse_imaging_info(data):
    """解析ImagingInfo2结构体的关键字段"""
    try:
        # 解析前10个int字段（40字节）
        header = struct.unpack_from('10i', data, 0)
        device_id = header[0]
        device_type = header[1]
        equip_type = header[2]
        planned_end_time = header[7]  # 第8个字段（索引7）
        image_num = header[8]
        selected_image_index = header[9]
        
        logging.debug(f"解析成功: device_id={device_id}, device_type={device_type}, "
                     f"equip_type={equip_type}, image_num={image_num}, "
                     f"selected_index={selected_image_index}, end_time={planned_end_time}")
        
        return device_id, planned_end_time
    except struct.error as e:
        logging.error(f"解析错误: {e}")
        return None, None

def calculate_unix_time(planned_end_time):
    """计算Unix时间戳（毫秒）"""
    return planned_end_time * 1000 + REFERENCE_TIMESTAMP

def rename_files(file_indexer, device_id, unix_time):
    """重命名图片和JSON文件到目标文件名"""
    # 目标文件名
    target_img = f"{device_id}_0_{unix_time}.jpg"
    target_json = f"{device_id}_0_{unix_time}.json"
    target_img_path = os.path.join(IMAGE_DIR, target_img)
    target_json_path = os.path.join(IMAGE_DIR, target_json)
    
    # 检查目标文件是否已存在
    if os.path.exists(target_img_path):
        logging.info(f"目标图片已存在，跳过重命名: {target_img}")
    else:
        # 在索引中查找最接近的图片文件
        img_file = file_indexer.find_closest_file(device_id, unix_time, "image")
        if img_file:
            img_path = os.path.join(IMAGE_DIR, img_file)
            try:
                os.rename(img_path, target_img_path)
                logging.info(f"重命名图片: {img_file} -> {target_img}")
                # 更新索引
                file_indexer.update_index_after_rename(device_id, img_file, target_img, "image")
            except FileNotFoundError:
                logging.warning(f"图片文件不存在: {img_path}")
            except Exception as e:
                logging.error(f"重命名图片错误: {e}")
        else:
            logging.warning(f"未找到匹配的图片文件: device_id={device_id}")
    
    # 处理JSON文件
    if os.path.exists(target_json_path):
        logging.info(f"目标JSON已存在，跳过重命名: {target_json}")
    else:
        json_file = file_indexer.find_closest_file(device_id, unix_time, "json")
        if json_file:
            json_path = os.path.join(IMAGE_DIR, json_file)
            try:
                os.rename(json_path, target_json_path)
                logging.info(f"重命名JSON: {json_file} -> {target_json}")
                # 更新索引
                file_indexer.update_index_after_rename(device_id, json_file, target_json, "json")
            except FileNotFoundError:
                logging.warning(f"JSON文件不存在: {json_path}")
            except Exception as e:
                logging.error(f"重命名JSON错误: {e}")
        else:
            logging.warning(f"未找到匹配的JSON文件: device_id={device_id}")
    
    return True

def get_local_ip():
    """获取本地IP地址（用于组播绑定）"""
    try:
        # 创建一个临时socket来获取本地IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))  # 连接到公共DNS服务器
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "0.0.0.0"  # 如果获取失败，使用默认地址

def udp_server_multicast(file_indexer):
    """UDP组播服务器主循环"""
    ensure_directory_exists(IMAGE_DIR)
    
    try:
        # 获取本地IP地址
        local_ip = get_local_ip()
        logging.info(f"检测到本地IP地址: {local_ip}")
        
        # 创建UDP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)
        hostIP = socket.inet_ntoa(ioctl(sock.fileno(), 0x8915, struct.pack('64s', 'br0'.encode('utf-8')))[20:24])
        print("create hostIP success, hostIP: " + hostIP)
        ip = socket.inet_aton(hostIP)
        # 设置套接字选项
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # 绑定到本地地址和端口
        sock.bind((MULTICAST_ADDR, UDP_PORT))
        logging.info(f"绑定到本地地址: {MULTICAST_ADDR}:{UDP_PORT}")
        
        # 加入组播组
        group = socket.inet_aton(MULTICAST_ADDR)
        mreq = struct.pack("=4s4s", group, ip)
        sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)
        
        # 设置为非阻塞模式
        sock.setblocking(False)
        
        logging.info(f"组播接收端启动，监听组播地址: {MULTICAST_ADDR}:{UDP_PORT}")
        
        running = True
        last_activity_log = time.time()
        
        while running:
            try:
                # 使用 select.select 检测可读套接字
                readable, _, _ = select.select([sock], [], [], 0.5)  # 500ms超时
                
                if not readable:
                    # 超时处理：记录活动状态
                    current_time = time.time()
                    if current_time - last_activity_log > 30:  # 每30秒记录一次
                        logging.info("服务器运行中，等待组播数据...")
                        last_activity_log = current_time
                    continue
                
                for s in readable:
                    if s is sock:
                        # 有数据到达
                        try:
                            # 接收数据
                            data, addr = sock.recvfrom(BUFFER_SIZE)
                            logging.info(f"收到来自 {addr} 的组播数据包，长度: {len(data)} 字节")
                            last_activity_log = time.time()
                            
                            # 检查数据长度
                            if len(data) != BUFFER_SIZE:
                                logging.warning(f"数据包大小无效: 期望 {BUFFER_SIZE} 字节, 实际 {len(data)} 字节")
                                continue
                            
                            # 解析关键字段
                            device_id, planned_end_time = parse_imaging_info(data)
                            if device_id is None:
                                continue
                            
                            # 计算Unix时间戳
                            unix_time = calculate_unix_time(planned_end_time)
                            human_time = datetime.utcfromtimestamp(unix_time / 1000).strftime('%Y-%m-%d %H:%M:%S')
                            logging.info(f"解析成功: device_id={device_id}, "
                                         f"unix_time={unix_time} ({human_time} UTC)")
                            
                            # 查找并重命名文件
                            rename_files(file_indexer, device_id, unix_time)
                            
                        except socket.error as e:
                            if e.errno == socket.errno.EAGAIN or e.errno == socket.errno.EWOULDBLOCK:
                                # 没有数据可读，继续轮询
                                continue
                            else:
                                logging.exception(f"套接字错误: {e}")
                        except Exception as e:
                            logging.exception(f"处理数据包错误: {e}")
                
            except KeyboardInterrupt:
                logging.info("服务器被用户中断")
                running = False
            except Exception as e:
                logging.exception(f"服务器错误: {e}")
                running = False
        
        sock.close()
        logging.info("组播接收端已停止")
    
    except OSError as e:
        logging.error(f"网络错误: {e}")
        logging.error("请检查网络连接和接口状态")
    except Exception as e:
        logging.exception(f"服务器初始化错误: {e}")

if __name__ == "__main__":
    try:
        # 初始化文件索引器
        file_indexer = FileIndexer()
        
        # 确保目录存在
        ensure_directory_exists(IMAGE_DIR)
        
        # 构建文件索引
        file_indexer.build_index(IMAGE_DIR)
        
        # 启动UDP服务器
        udp_server_multicast(file_indexer)
    
    except KeyboardInterrupt:
        logging.info("程序被用户中断，正在退出...")
        exit(0)
    
    except Exception as e:
        logging.exception(f"程序发生未预期错误: {e}")
        exit(1)
