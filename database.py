#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模块 - 监听指定目录，解析不同格式文件内容，结构化存储到SQLite数据库
支持并发写入，文件类型可扩展

创建时间: 2025-01-25
"""

import configparser
import json
import logging
import os
import sqlite3
import time
import queue
import threading

from dateutil import parser
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from queue import Queue
from threading import Lock, Event
from typing import Dict, Any, Optional, List
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class DatabaseConnectionPool:
    """SQLite连接池管理器"""
    
    def __init__(self, db_path: str, max_connections: int = 10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.lock = Lock()
        self._initialize_pool()
        
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            conn = self._create_connection()
            if conn:
                self.connections.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # 优化SQLite设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=-64000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA auto_vacuum=INCREMENTAL")
            conn.execute("PRAGMA busy_timeout=30000")
            
            conn.row_factory = sqlite3.Row
            return conn
            
        except Exception as e:
            logging.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            with self.lock:
                if not self.connections.empty():
                    conn = self.connections.get_nowait()
                else:
                    conn = self._create_connection()
                    
            if conn is None:
                raise Exception("无法获取数据库连接")
                
            yield conn
            
        except Exception as e:
            logging.error(f"数据库操作错误: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                try:
                    conn.commit()
                    with self.lock:
                        if not self.connections.full():
                            self.connections.put(conn)
                        else:
                            conn.close()
                except Exception as e:
                    logging.error(f"归还连接时出错: {e}")
                    conn.close()
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            while not self.connections.empty():
                try:
                    conn = self.connections.get_nowait()
                    conn.close()
                except Exception as e:
                    logging.error(f"关闭连接时出错: {e}")

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config_path: str = "config.ini"):
        # 首先初始化日志器
        self.logger = logging.getLogger('DatabaseManager')
        
        self.config = self._load_config(config_path)
        self.db_config = self.config['database']
        self.insert_sample_data = self.db_config.get('insert_sample_data',False)
        # 初始化数据库路径
        self.db_path = self.db_config.get('path', 'database.db')
        if not os.path.isabs(self.db_path):
            self.db_path = os.path.abspath(self.db_path)
        
        # 初始化连接池
        max_connections = int(self.db_config.get('max_connections', 10))
        self.connection_pool = DatabaseConnectionPool(self.db_path, max_connections)
            
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section in config.sections():
            result[section] = dict(config[section])
        
        return result
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建源数据表，保存数据的输入信息-插入操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS origional_table (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sat_id TEXT,
                    sat_category TEXT,
                    sat_load_category TEXT,
                    planed_start_time TEXT,
                    planed_end_time TEXT,
                    file_size REAL,
                    file_name TEXT,
                    file_path TEXT,
                    image_truth_width REAL,
                    image_resolution REAL,
                    timestamp TEXT NOT NULL,
                    enu_base_longitude REAL,
                    enu_base_latitude REAL,
                    enu_base_altitude REAL,
                    sat_enu_x REAL,
                    sat_enu_y REAL,
                    sat_enu_z REAL,
                    cam_enu_x REAL,
                    cam_enu_y REAL,
                    cam_enu_z REAL,
                    cam_enu_w REAL,
                    sat_j2000_x REAL,
                    sat_j2000_y REAL,
                    sat_j2000_z REAL,
                    sat_qbi_x REAL,
                    sat_qbi_y REAL,
                    sat_qbi_z REAL,
                    sat_qbi_w REAL,
                    load_fov_x REAL,
                    load_fov_y REAL,
                    image_pixel_x REAL,
                    image_pixel_y REAL,
                    UNIQUE (sat_id, timestamp)
                )
            ''')

            # 创建真值表-插入操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS truth_table (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sat_id TEXT,
                    timestamp TEXT NOT NULL,
                    target_id TEXT,
                    fleet_number TEXT,
                    target_category TEXT,
                    target_direction TEXT,
                    target_longitude REAL,
                    target_latitude REAL,
                    target_altitude REAL,
                    target_sat_z REAL,
                    target_sat_y REAL,
                    target_sat_x REAL,
                    target_x REAL,
                    target_y REAL,
                    target_width REAL,
                    target_height REAL,
                    UNIQUE (target_id, timestamp)
                    )
                ''')

            # 创建特征表，保存算法的中间结果信息--插入+更新操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feature_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT NOT NULL,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_image_path TEXT,
                        target_truth BOOL,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_fusion_longitude REAL,
                        target_fusion_latitude REAL,
                        target_longitude_speed REAL,
                        target_latitude_speed REAL,
                        target_total_speed REAL,
                        target_npy_path TEXT,
                        UNIQUE (target_id, timestamp)
                    )
                ''')

            # 创建决策表，保存数据的决策信息--只有插入操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS decision_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT,
                        query_days TEXT,
                        fleet_number TEXT,
                        speed_threat REAL,
                        heading_threat REAL,
                        distance_threat REAL,
                        heading_angle REAL,
                        speed REAL,
                        lat REAL,
                        lon REAL,
                        intention TEXT
                    )
                ''')

            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_origional_timestamp ON origional_table(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_origional_sat_id ON origional_table(sat_id)')

            cursor.execute('CREATE INDEX IF NOT EXISTS idx_truth_sat_id ON truth_table(sat_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_truth_timestamp ON truth_table(timestamp)')
            
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_feature_sat_id ON feature_table(sat_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_feature_target_id ON feature_table(target_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_feature_timestamp ON feature_table(timestamp)')

            cursor.execute('CREATE INDEX IF NOT EXISTS idx_decision_timestamp ON decision_table(timestamp)')
            
            conn.commit()
            

            self.logger.info("数据库表结构初始化完成")
    
    def insert_origin_sample_data(self, num_records=5):
        """向origional_table插入随机生成的示例数据"""
        try:
            import random
            from datetime import datetime, timedelta, timezone
            
            device_type_map = {0: 'GEO', 131072: 'LEO', 131073: 'LEO_SAR', 131074: 'LEO_RADAR', 131075: 'LEO_ELECTRO'}
            equip_type_map = {1: 'CCD', 2: 'IR', 3: 'SAR', 4: 'RADAR', 5: 'ELECTRO', 6: 'PAN'}
            
            success_count = 0
            failed_count = 0
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 生成随机时间戳（过去30天内）
                        base_time = datetime.now(timezone.utc)
                        random_days = random.randint(0, 30)
                        random_hours = random.randint(0, 23)
                        random_minutes = random.randint(0, 59)
                        
                        random_time = base_time - timedelta(
                            days=random_days, 
                            hours=random_hours, 
                            minutes=random_minutes
                        )
                        
                        # 转换为UTC毫秒级时间戳
                        timestamp_ms = str(int(random_time.timestamp() * 1000))
                        
                        # 生成随机卫星数据
                        origin_data = {
                            'sat_id': f'SAT_{random.randint(1000, 9999)}',
                            'sat_category': random.choice(list(device_type_map.values())),
                            'sat_load_category': random.choice(list(equip_type_map.values())),
                            'planed_start_time': str(int(random_time.timestamp()) - 3600),
                            'planed_end_time': str(int(random_time.timestamp()) + 3600),
                            'timestamp': timestamp_ms,
                            'file_size': round(random.uniform(10.0, 1000.0), 2),
                            'file_name': f'image_{timestamp_ms}.jpg',
                            'file_path': f'/data/images/{timestamp_ms}.jpg',
                            'image_truth_width': round(random.uniform(100.0, 1000.0), 2),
                            'image_resolution': round(random.uniform(0.1, 10.0), 2),
                            'enu_base_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'enu_base_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'enu_base_altitude': round(random.uniform(0.0, 10000.0), 2),
                            'sat_enu_x': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_enu_y': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_enu_z': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'cam_enu_x': round(random.uniform(-1.0, 1.0), 6),
                            'cam_enu_y': round(random.uniform(-1.0, 1.0), 6),
                            'cam_enu_z': round(random.uniform(-1.0, 1.0), 6),
                            'cam_enu_w': round(random.uniform(-1.0, 1.0), 6),
                            'sat_j2000_x': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_j2000_y': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_j2000_z': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_qbi_x': round(random.uniform(-1.0, 1.0), 6),
                            'sat_qbi_y': round(random.uniform(-1.0, 1.0), 6),
                            'sat_qbi_z': round(random.uniform(-1.0, 1.0), 6),
                            'sat_qbi_w': round(random.uniform(-1.0, 1.0), 6),
                            'load_fov_x': round(random.uniform(1.0, 90.0), 2),
                            'load_fov_y': round(random.uniform(1.0, 90.0), 2),
                            'image_pixel_x': random.randint(1000, 4000),
                            'image_pixel_y': random.randint(1000, 4000)
                        }
                        
                        # 插入数据
                        record_id = self.insert_origin_data(origin_data)
                        if record_id:
                            success_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入源示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"源示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入源示例数据总体失败: {e}")
            return 0

    def insert_truth_sample_data(self, num_records=10):
        """向truth_table插入随机生成的示例数据"""
        try:
            import random
            from datetime import datetime, timedelta, timezone
            
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            # 获取现有的卫星ID和时间戳用于关联
            sat_ids = []
            timestamps = []
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT sat_id, timestamp FROM origional_table LIMIT 10")
                rows = cursor.fetchall()
                for row in rows:
                    sat_ids.append(row['sat_id'])
                    timestamps.append(row['timestamp'])
            
            # 如果没有源数据，创建一些虚拟数据
            if not sat_ids:
                sat_ids = [f'SAT_{random.randint(1000, 9999)}' for _ in range(3)]
                base_time = datetime.now(timezone.utc)
                timestamps = [str(int((base_time - timedelta(days=i)).timestamp() * 1000)) for i in range(3)]
            
            success_count = 0
            failed_count = 0
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 随机选择卫星ID和时间戳
                        sat_id = random.choice(sat_ids)
                        timestamp = random.choice(timestamps)
                        
                        # 生成随机目标数据
                        truth_data = {
                            'sat_id': sat_id,
                            'timestamp': timestamp,
                            'target_id': f'TARGET_{random.randint(10000, 99999)}',
                            'fleet_number': f'FLEET_{random.randint(1, 5):02d}',
                            'target_category': random.choice(list(target_type_map.values())),
                            'target_direction': str(round(random.uniform(0.0, 360.0), 1)),
                            'target_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'target_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'target_altitude': round(random.uniform(0.0, 10000.0), 2),
                            'target_sat_x': round(random.uniform(-1000.0, 1000.0), 2),
                            'target_sat_y': round(random.uniform(-1000.0, 1000.0), 2),
                            'target_sat_z': round(random.uniform(-1000.0, 1000.0), 2),
                            'target_x': round(random.uniform(0.0, 1000.0), 2),
                            'target_y': round(random.uniform(0.0, 1000.0), 2),
                            'target_width': round(random.uniform(10.0, 100.0), 2),
                            'target_height': round(random.uniform(10.0, 100.0), 2)
                        }
                        
                        # 插入数据
                        record_id = self.insert_truth_data(truth_data)
                        if record_id:
                            success_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入真值示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"真值示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入真值示例数据总体失败: {e}")
            return 0

    def insert_feature_sample_data(self, num_records=15):
        """向feature_table插入随机生成的示例数据"""
        try:
            import random
            from datetime import datetime, timedelta, timezone
            
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            # 获取现有的目标ID用于关联
            target_ids = []
            sat_ids = []
            timestamps = []
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT target_id, sat_id, timestamp FROM truth_table LIMIT 10")
                rows = cursor.fetchall()
                for row in rows:
                    target_ids.append(row['target_id'])
                    sat_ids.append(row['sat_id'])
                    timestamps.append(row['timestamp'])
            
            # 如果没有真值数据，创建一些虚拟数据
            if not target_ids:
                target_ids = [f'TARGET_{random.randint(10000, 99999)}' for _ in range(5)]
                sat_ids = [f'SAT_{random.randint(1000, 9999)}' for _ in range(3)]
                base_time = datetime.now(timezone.utc)
                timestamps = [str(int((base_time - timedelta(days=i)).timestamp() * 1000)) for i in range(3)]
            
            success_count = 0
            failed_count = 0
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 随机选择目标ID、卫星ID和时间戳
                        target_id = random.choice(target_ids)
                        sat_id = random.choice(sat_ids)
                        # timestamp = random.choice(timestamps)
                        timestamp = 111 + i
                        # 生成随机特征数据
                        feature_data = {
                            'sat_id': sat_id,
                            'target_id': target_id,
                            'timestamp': timestamp,
                            'fleet_number': f'FLEET_{random.randint(1, 5):02d}',
                            'target_category': random.choice(list(target_type_map.values())),
                            'target_image_path': f'/data/targets/{target_id}_{timestamp}.jpg',
                            'target_truth': random.choice([True, False]),
                            'target_x': round(random.uniform(0.0, 1000.0), 2),
                            'target_y': round(random.uniform(0.0, 1000.0), 2),
                            'target_width': round(random.uniform(10.0, 100.0), 2),
                            'target_height': round(random.uniform(10.0, 100.0), 2),
                            'target_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'target_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'target_fusion_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'target_fusion_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'target_longitude_speed': round(random.uniform(-10.0, 10.0), 2),
                            'target_latitude_speed': round(random.uniform(-10.0, 10.0), 2),
                            'target_total_speed': round(random.uniform(0.0, 20.0), 2),
                            'target_npy_path': f'/data/features/{target_id}_{timestamp}.npy'
                        }
                        
                        # 插入数据
                        record_id = self.insert_feature_data(feature_data)
                        if record_id:
                            success_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入特征示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"特征示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入特征示例数据总体失败: {e}")
            return 0
    
    def insert_decision_random_sample_data(self, num_records=10):
        """向decision_table插入随机生成的示例数据
        
        :param num_records: 要插入的记录数量，默认为10
        :return: 成功插入的记录数量
        """
        try:
            import random
            import time
            from datetime import datetime, timedelta, timezone
            
            # 当前时间作为基准
            base_time = datetime.now(timezone.utc)
            
            # 可能的舰队编号和意图
            fleet_numbers = ['FLEET_001', 'FLEET_002', 'FLEET_003', 'FLEET_004', 'FLEET_005']
            intentions = ['正常', '可疑', '高度可疑', '威胁', '未知']
            query_days_options = ['1', '3', '5', '7', '10', '14', '30']
            
            success_count = 0
            failed_count = 0

            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 生成随机时间戳（过去30天内）
                        random_days = random.randint(0, 30)
                        random_hours = random.randint(0, 23)
                        random_minutes = random.randint(0, 59)
                        
                        random_time = base_time - timedelta(
                            days=random_days, 
                            hours=random_hours, 
                            minutes=random_minutes
                        )
                        
                        # 转换为UTC毫秒级时间戳
                        # timestamp_ms = str(int(random_time.timestamp() * 1000))
                        timestamp_ms = 111 + i
                        self.logger.info(f"时间戳为：{timestamp_ms}")
                        # 生成随机数据
                        data = {
                            'timestamp': timestamp_ms,
                            'query_days': random.choice(query_days_options),
                            'fleet_number': random.choice(fleet_numbers),
                            'speed_threat': round(random.uniform(0.0, 1.0), 2),
                            'heading_threat': round(random.uniform(0.0, 1.0), 2),
                            'distance_threat': round(random.uniform(0.0, 1.0), 2),
                            'heading_angle': round(random.uniform(0.0, 360.0), 1),
                            'speed': round(random.uniform(5.0, 40.0), 1),
                            'lat': round(random.uniform(-90.0, 90.0), 6),
                            'lon': round(random.uniform(-180.0, 180.0), 6),
                            'intention': random.choice(intentions)
                        }
                        
                        # 构建插入SQL
                        columns = list(data.keys())
                        values = list(data.values())
                        placeholders = ', '.join(['?'] * len(columns))
                        
                        insert_sql = f"""
                            INSERT INTO decision_table ({', '.join(columns)})
                            VALUES ({placeholders})
                        """
                        
                        cursor.execute(insert_sql, values)
                        success_count += 1
                        
                        # 每插入10条记录记录一次日志
                        if (i + 1) % 10 == 0:
                            self.logger.debug(f"已插入 {i + 1} 条决策示例数据")
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入决策示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"决策示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入决策示例数据总体失败: {e}")
            return 0
        
    def insert_origin_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入源数据
        
        :param data: 源数据字典，可包含任意origional_table的字段
        :return: 插入记录的ID，失败返回None
        """
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 定义origional_table的所有列（除了自增主键），匹配实际表结构
                all_columns = {
                    'sat_id': 'TEXT',
                    'sat_category': 'TEXT',
                    'sat_load_category': 'TEXT',
                    'planed_start_time': 'TEXT',
                    'planed_end_time': 'TEXT',
                    'file_size': 'REAL',
                    'file_name': 'TEXT',
                    'file_path': 'TEXT',
                    'image_truth_width': 'REAL',
                    'image_resolution': 'REAL',
                    'timestamp': 'TEXT NOT NULL',
                    'enu_base_longitude': 'REAL',
                    'enu_base_latitude': 'REAL',
                    'enu_base_altitude': 'REAL',
                    'sat_enu_x': 'REAL',
                    'sat_enu_y': 'REAL',
                    'sat_enu_z': 'REAL',
                    'cam_enu_x': 'REAL',
                    'cam_enu_y': 'REAL',
                    'cam_enu_z': 'REAL',
                    'cam_enu_w': 'REAL',
                    'sat_j2000_x': 'REAL',
                    'sat_j2000_y': 'REAL',
                    'sat_j2000_z': 'REAL',
                    'sat_qbi_x': 'REAL',
                    'sat_qbi_y': 'REAL',
                    'sat_qbi_z': 'REAL',
                    'sat_qbi_w': 'REAL',
                    'load_fov_x': 'REAL',
                    'load_fov_y': 'REAL',
                    'image_pixel_x': 'REAL',
                    'image_pixel_y': 'REAL'
                }
                
                # 检查必须字段
                required_fields = ['sat_id', 'timestamp']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if 'INTEGER' in all_columns[column]:
                            # 整型处理
                            try:
                                values_to_insert.append(int(float(str(value))) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        elif 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT OR IGNORE INTO origional_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入源数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入源数据，记录ID: {record_id}")
                return record_id
                    
        except Exception as e:
            self.logger.error(f"插入源数据失败: {e}")
            return None
    
    def insert_truth_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入真值数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                # 定义truth_table的所有列（除了自增主键），匹配实际表结构
                all_columns = {
                    'sat_id': 'TEXT',
                    'timestamp': 'TEXT NOT NULL',
                    'target_id': 'TEXT',
                    'fleet_number': 'TEXT',
                    'target_category': 'TEXT',
                    'target_direction': 'TEXT',
                    'target_longitude': 'REAL',
                    'target_latitude': 'REAL',
                    'target_altitude': 'REAL',
                    'target_sat_z': 'REAL',
                    'target_sat_y': 'REAL',
                    'target_sat_x': 'REAL',
                    'target_x': 'REAL',
                    'target_y': 'REAL',
                    'target_width': 'REAL',
                    'target_height': 'REAL'
                }
                
                # 检查必须字段
                required_fields = ['sat_id', 'timestamp']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT OR IGNORE INTO truth_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入真值数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入真值数据，记录ID: {record_id}")
                return record_id
                
        except Exception as e:
            self.logger.error(f"插入真值数据失败: {e}")
    
    def insert_feature_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入特征数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 定义feature_table的所有列（除了自增主键）
                all_columns = {
                    'sat_id': 'TEXT NOT NULL',
                    'fleet_number': 'TEXT',
                    'target_category': 'TEXT',
                    'target_id': 'TEXT',
                    'timestamp': 'TEXT NOT NULL',
                    'target_image_path': 'TEXT',
                    'target_truth': 'BOOL',
                    'target_x': 'REAL',
                    'target_y': 'REAL',
                    'target_width': 'REAL',
                    'target_height': 'REAL',
                    'target_longitude': 'REAL',
                    'target_latitude': 'REAL',
                    'target_fusion_longitude': 'REAL',
                    'target_fusion_latitude': 'REAL',
                    'target_longitude_speed': 'REAL',
                    'target_latitude_speed': 'REAL',
                    'target_total_speed': 'REAL',
                    'target_npy_path': 'TEXT',
                    'target_json_path': 'TEXT'
                }
                
                # 检查必须字段
                required_fields = ['sat_id', 'timestamp']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if all_columns[column] == 'BOOL':
                            # 布尔值处理
                            if isinstance(value, bool):
                                values_to_insert.append(value)
                            elif isinstance(value, str):
                                values_to_insert.append(value.lower() in ['true', '1', 'yes', 'real'])
                            else:
                                values_to_insert.append(bool(value))
                        elif 'INTEGER' in all_columns[column]:
                            # 整型处理
                            try:
                                values_to_insert.append(int(float(str(value))) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        elif 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT OR IGNORE INTO feature_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入特征数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入特征数据，记录ID: {record_id}")
                return record_id
        except Exception as e:
            self.logger.error(f"插入特征数据失败: {e}")
    
    def insert_decision_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入决策数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                all_columns = {
                    'timestamp': 'TEXT',
                    'query_days': 'TEXT',
                    'fleet_number': 'TEXT',
                    'speed_threat': 'REAL',
                    'heading_threat': 'REAL',
                    'distance_threat': 'REAL',
                    'heading_angle': 'REAL',
                    'speed': 'REAL',
                    'lat': 'REAL',
                    'lon': 'REAL',
                    'intention': 'TEXT'
                }
                
                # 检查必须字段
                required_fields = ['timestamp', 'fleet_number']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if 'INTEGER' in all_columns[column]:
                            # 整型处理
                            try:
                                values_to_insert.append(int(float(str(value))) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        elif 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT OR IGNORE INTO decision_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入决策数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入决策数据，记录ID: {record_id}")
                return record_id
                    
        except Exception as e:
            self.logger.error(f"插入决策数据失败: {e}")
    
    def update_WZZH(self, sat_id: str, timestamp: str, target_id: str, longitude: float, latitude: float) -> bool:
        """
        更新位置转换(WZZH)算法结果到feature_table
        
        :param sat_id: 卫星ID
        :param timestamp: 时间戳
        :param target_id: 目标ID
        :param longitude: 经度
        :param latitude: 纬度
        :return: 更新是否成功
        """
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查记录是否存在
                cursor.execute("""
                    SELECT id FROM feature_table 
                    WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                """, (sat_id, timestamp, target_id))
                
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 更新现有记录
                    update_sql = """
                        UPDATE feature_table 
                        SET target_longitude = ?, target_latitude = ?
                        WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                    """
                    
                    cursor.execute(update_sql, (longitude, latitude, sat_id, timestamp, target_id))
                    conn.commit()
                    
                    self.logger.info(f"WZZH更新位置信息成功: target_id={target_id}, "
                                   f"lat={latitude}, lon={longitude} (ID: {existing_record['id']})")
                    return True
                else:
                    # 插入新记录
                    feature_data = {
                        'sat_id': sat_id,
                        'target_id': target_id,
                        'timestamp': timestamp,
                        'target_longitude': longitude,
                        'target_latitude': latitude
                    }
                    
                    record_id = self.insert_feature_data(feature_data)
                    if record_id:
                        self.logger.info(f"WZZH插入新的位置记录: target_id={target_id}, "
                                       f"lat={latitude}, lon={longitude} (ID: {record_id})")
                        return True
                    else:
                        self.logger.error(f"WZZH插入新记录失败: target_id={target_id}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"WZZH更新位置信息失败: {e}")
            return False

    def update_RHDW(self, sat_id: str, timestamp: str, target_id: str, fusion_longitude: float, 
                    fusion_latitude: float, longitude_speed: float = None, latitude_speed: float = None, 
                    total_speed: float = None, target_category: str = None) -> bool:
        """
        更新融合定位(RHDW)算法结果到feature_table
        
        :param sat_id: 卫星ID
        :param timestamp: 时间戳
        :param target_id: 目标ID
        :param fusion_longitude: 融合经度
        :param fusion_latitude: 融合纬度
        :param longitude_speed: 经度速度
        :param latitude_speed: 纬度速度
        :param total_speed: 总速度
        :param target_category: 目标类别
        :return: 更新是否成功
        """
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查记录是否存在
                cursor.execute("""
                    SELECT id FROM feature_table 
                    WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                """, (sat_id, timestamp, target_id))
                
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 构建动态更新SQL
                    update_fields = ["target_fusion_longitude = ?", "target_fusion_latitude = ?"]
                    update_values = [fusion_longitude, fusion_latitude]
                    
                    if longitude_speed is not None:
                        update_fields.append("target_longitude_speed = ?")
                        update_values.append(longitude_speed)
                    
                    if latitude_speed is not None:
                        update_fields.append("target_latitude_speed = ?")
                        update_values.append(latitude_speed)
                    
                    if total_speed is not None:
                        update_fields.append("target_total_speed = ?")
                        update_values.append(total_speed)
                    
                    if target_category is not None:
                        update_fields.append("target_category = ?")
                        update_values.append(target_category)
                    
                    # 添加WHERE条件的值
                    update_values.extend([sat_id, timestamp, target_id])
                    
                    update_sql = f"""
                        UPDATE feature_table 
                        SET {', '.join(update_fields)}
                        WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                    """
                    
                    cursor.execute(update_sql, update_values)
                    conn.commit()
                    
                    self.logger.info(f"TSFX更新融合位置信息成功: target_id={target_id}, "
                                   f"fusion_lon={fusion_longitude}, fusion_lat={fusion_latitude}, "
                                   f"total_speed={total_speed} (ID: {existing_record['id']})")
                    return True
                else:
                    # 插入新记录
                    feature_data = {
                        'sat_id': sat_id,
                        'target_id': target_id,
                        'timestamp': timestamp,
                        'target_fusion_longitude': fusion_longitude,
                        'target_fusion_latitude': fusion_latitude,
                        'target_truth': True
                    }
                    
                    # 添加可选字段
                    if longitude_speed is not None:
                        feature_data['target_longitude_speed'] = longitude_speed
                    if latitude_speed is not None:
                        feature_data['target_latitude_speed'] = latitude_speed
                    if total_speed is not None:
                        feature_data['target_total_speed'] = total_speed
                    if target_category is not None:
                        feature_data['target_category'] = target_category
                    
                    record_id = self.insert_feature_data(feature_data)
                    if record_id:
                        self.logger.info(f"TSFX插入新的融合位置记录: target_id={target_id}, "
                                       f"fusion_lon={fusion_longitude}, fusion_lat={fusion_latitude}, "
                                       f"total_speed={total_speed} (ID: {record_id})")
                        return True
                    else:
                        self.logger.error(f"TSFX插入新记录失败: target_id={target_id}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"TSFX更新融合位置信息失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接池"""
        self.connection_pool.close_all()
        self.logger.info("数据库连接池已关闭")