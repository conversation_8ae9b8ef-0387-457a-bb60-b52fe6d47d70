import json
import threading
import subprocess
import shlex
import socket
import sys
import os
import time
import logging
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
from urllib.parse import parse_qs, urlparse, unquote
from overlap_new import slice_image_with_overlap

processes_lock = threading.Lock()

# 全局字典保存运行中的进程信息
running_processes = {}
id_to_pid_mapping = {}

# 配置日志
timestamp0 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
LOG_DIR = f"/home/<USER>/log/http_server_{timestamp0}.log"  # 固定的日志存放路径
os.makedirs(LOG_DIR, exist_ok=True)  # 确保日志目录存在

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, 'http_server.log')),
        logging.StreamHandler()
    ]
)

def log_request_response(request_data, response_data):
    """记录HTTP请求和响应到日志"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"\n=== Request ===\n{timestamp}\n{json.dumps(request_data, indent=2)}\n"
        log_entry += f"\n=== Response ===\n{timestamp}\n{json.dumps(response_data, indent=2)}\n"
        logging.info(log_entry)
    except Exception as e:
        logging.error(f"记录日志失败: {str(e)}")

def find_available_port(start_port=6000, end_port=7000):
        """在指定范围内查找可用端口"""
        for port in range(start_port, end_port + 1):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('', port))
                    return port
            except socket.error:
                continue
        raise Exception(f"No available ports found between {start_port} and {end_port}")
        
def run_python_script_with_args(script_path, args=None, task_id=None, device_id=None, startime=None, model_id=None):
    """运行指定的Python脚本（支持命令行参数）并返回进程ID和退出状态"""
    if not os.path.exists(script_path):
        print(f"错误: 文件 {script_path} 不存在")
        return None, -1
    time0 = 1
    if "v8" in script_path:
        time0 = 6
    elif "v11" in script_path:
        time0 = 6
    elif "v12" in script_path:
        time0 = 7
    elif "rf" in script_path:
        time0 = 19
    elif "nano" in script_path:
        time0 = 11
    elif "v9" in script_path:
        time0 = 10
    elif "sgf" in script_path:
        time0 = 6
    elif "dq" in script_path:
        time0 = 300
    elif "dino" in script_path:
        time0 = 13
    try:
        conda_init_path = "/opt/conda/etc/profile.d/conda.sh"
        conda_env_name = "ostrack"
        base_cmd = f'source {conda_init_path} && conda activate {conda_env_name} && {sys.executable} {script_path}'
        base_cmd = f'{sys.executable} {script_path}'
        if task_id is not None:
            args.extend(['--task_id', str(task_id)])
        if device_id is not None:
            args.extend(['--device_id', str(device_id)])  
        if startime is not None:
            args.extend(['--startime', str(startime)])  
        if args:
            quoted_args = ' '.join([shlex.quote(str(arg)) for arg in args])
            base_cmd += f' {quoted_args}'
        
        cmd = ['bash', '-c', base_cmd]
        process = subprocess.Popen(cmd, env=os.environ.copy())
        pid = process.pid
        start_time = time.time()
        with processes_lock:
            running_processes[pid] = {
                'process': process,
                'start_time': start_time,
                'time0': time0,
                'end_time': None,
                'script_path': script_path,
                'args': args,
                'program': 'running',
                'monitor_thread': None,
                'task_id': task_id
            }
            if task_id is not None:
                id_to_pid_mapping[str(task_id)] = pid
            
        return pid, None
        
    except Exception as e:
        print(f"启动脚本 {script_path} 失败: {str(e)}")
        return None, -1

def monitor_process(pid):
    """监控进程状态的线程函数"""
    if pid not in running_processes:
        logging.warning(f"尝试监控不存在的进程: {pid}")
        return
    
    process = running_processes[pid]['process']
    
    try:
        return_code = process.wait()
        end_time = time.time()
        task_id = running_processes[pid]['task_id']
        # 读取进程的输出和错误
        stdout, stderr = process.communicate()
        stdout = stdout.decode('utf-8') if stdout else ""
        stderr = stderr.decode('utf-8') if stderr else ""
        
        if return_code == 0:
            msg = f"脚本 (ID: {task_id}) 正常退出"
            logging.info(msg)
            with processes_lock:
                running_processes[pid]['program'] = 'completed'
            if stdout:
                logging.info(f"ID {task_id} 标准输出:\n{stdout}")
        else:
            msg = f"脚本 (ID: {task_id}) 异常退出，返回码: {return_code}"
            if stderr:
                msg += f"\n错误输出:\n{stderr}"
                logging.error(msg)
            with processes_lock:
                running_processes[pid]['program'] = 'error'
                running_processes[pid]['error'] = stderr
        with processes_lock:
            running_processes[pid]['end_time'] = end_time
            running_processes[pid]['stdout'] = stdout
            running_processes[pid]['stderr'] = stderr
            
    except Exception as e:
        error_msg = f"监控进程 {task_id} 时出错: {str(e)}"
        logging.error(error_msg)
        with processes_lock:
            running_processes[pid]['program'] = 'error'
            running_processes[pid]['end_time'] = time.time()
            running_processes[pid]['error'] = str(e)

def check_process_status(pid, wait_time=1):
    """检查进程状态"""
    if pid not in running_processes:
        return 1
    
    process = running_processes[pid]['process']
    
    try:
        return_code = process.wait(timeout=wait_time)
        
        if return_code == 0:
            msg = f"脚本 (PID: {pid}) 正常退出"
            logging.info(msg)
            with processes_lock:
                running_processes[pid]['program'] = 'completed'
                running_processes[pid]['end_time'] = time.time()
            return 0
        else:
            msg = f"脚本 (PID: {pid}) 异常退出，返回码: {return_code}"
            logging.error(msg)
            with processes_lock:
                running_processes[pid]['program'] = 'error'
                running_processes[pid]['end_time'] = time.time()
            return 1
            
    except subprocess.TimeoutExpired:
        if running_processes[pid]['monitor_thread'] is None:
            monitor_thread = threading.Thread(
                target=monitor_process, 
                args=(pid,),
                daemon=True
            )
            monitor_thread.start()
            with processes_lock:
                running_processes[pid]['monitor_thread'] = monitor_thread
        return 0

def get_process_info(pid_or_id):
    """获取运行中进程的信息"""
    if str(pid_or_id) in id_to_pid_mapping:
        pid = id_to_pid_mapping[str(pid_or_id)]
    else:
        pid = pid_or_id
    if pid not in running_processes:
        return {"running": False, "error": "进程不存在"}
    
    process_info = running_processes[pid]
    script_path = process_info['script_path']
    if 'distributed' in script_path:
        # 分布式任务的 time0 计算逻辑
        time0 = 150  # 分布式任务默认超时时间
    else:
        # 普通脚本任务的 time0 计算逻辑
        time0 = process_info.get('time0', 1)  # 使用原逻辑中的 time0
    
    if process_info['end_time'] is not None:
        elapsed_time = process_info['end_time'] - process_info['start_time']
    else:
        elapsed_time = time.time() - process_info['start_time']
    
    task_progress = int((elapsed_time/time0) * 100)
    if process_info['program'] == "completed":
        task_progress = 100
    return {
        "task_id": process_info.get('task_id'),
        "elapsed_time": elapsed_time,
        "task_progress": min(task_progress, 100),  # 确保不超过100%
        "program": process_info['program'],
        "task_type": "distributed" if 'distributed' in script_path.lower() else "script"
    }

def task_run_script(params):
    """运行Python脚本的任务"""
    required_params = ['script_path']
    for param in required_params:
        if param not in params:
            error_msg = f"缺少必要参数: {param}"
            logging.error(error_msg)
            return {
                "status": "fail", 
                "pid": None,
                "message": "参数错误",
                "code": "1"
            }
    
    script_path = params['script_path']
    args = params.get('args', [])
    timeout = params.get('timeout')
    task_id = params.get('task_id')
    device_id = params.get('device_id')
    startime = params.get('startime')
    model_id = 1
    logging.info(f"开始执行脚本任务: 脚本路径={script_path}, 参数={args}, 任务ID={task_id}")
    if "fusion" in script_path or "CTF" in script_path:
        model_id = 15
    else: 
        index = args.index('--input') + 1
        input_path = args[index]
        json_file = None
        if input_path and os.path.exists(input_path):
            if os.path.isfile(input_path):
                input_dir = os.path.dirname(input_path)
            else:
                input_dir = input_path
            # 查找JSON文件
            for f in os.listdir(input_dir):
                if f.lower().endswith('.json'):
                    json_file = os.path.join(input_dir, f)
                    break
    
        # 如果找到JSON文件，添加到参数中
        if json_file:
            args.extend(['--json', json_file])
        else:
            error_msg = "未找到切片JSON文件"
            logging.error(error_msg)
            return {
                "status": "fail", 
                "pid": None,
                "message": "参数错误",
                "code": "1"
            }
    print(f'args:{args}')
    try:
        pid, _ = run_python_script_with_args(script_path, args, task_id, device_id, startime, model_id)
    except Exception as e:
        error_msg = f"处理输入参数错误: {str(e)}"
        logging.error(error_msg)
    if pid is None:
        error_msg = "任务进程创建失败"
        logging.error(error_msg)
        return {
            "status": "fail",
            "pid": None,
            "message": "任务进程创建失败或异常退出",
            "code": "2"
        }
    
    time.sleep(1)
    status = check_process_status(pid)
    
    if status == 0:
        if running_processes[pid]['program'] == 'running':
            logging.info(f"任务启动成功: PID={pid}, 任务ID={task_id}")
            return {
                "status": "success",
                "pid": pid,
                "id": task_id,
                "message": "任务进程正在运行",
                "code": "0"
            }
        elif running_processes[pid]['program'] == 'completed':
            logging.info(f"任务完成: PID={pid}, 任务ID={task_id}")
            return {
                "status": "success",
                "pid": pid,
                "id": task_id,
                "message": "任务进程已完成",
                "code": "0"
            }
        else:
            error_msg = running_processes[pid].get('error', "任务进程创建失败或异常退出")
            logging.error(f"任务失败: PID={pid}, 错误={error_msg}")
            return {
                "status": "fail",
                "pid": pid,
                "id": task_id,
                "message": "任务进程创建失败或异常退出",
                "code": "2"
            }
    else:
        error_msg = running_processes[pid].get('error', "任务进程创建失败或异常退出")
        logging.error(f"任务失败: PID={pid}, 错误={error_msg}")
        
        return {
            "status": "fail",
            "pid": pid,
            "id": task_id,
            "message": "任务进程创建失败或异常退出",
            "code": "2"
        }

def task_get_process(params):
    """获取进程信息的任务"""
    if 'pid' not in params and 'id' not in params:
        return {
            "status": "fail",
            "process_info": None,
            "message": "缺少pid参数",
            "code": "1"
        }
    
    if 'id' in params:
        pid_or_id = params['id']
    else:
        pid_or_id = params['pid']
        
    try:
        pid_or_id = int(pid_or_id)
    except Exception as e:
        error_msg = str(e)
        logging.error(f"任务失败: {pid_or_id}, 错误={error_msg}")
        return {
            "status": "fail",
            "process_info": None,
            "message": "任务id必须是整数",
            "code": "1"
        }
    
    process_info = get_process_info(pid_or_id)
    if "error" in process_info:
        return {
            "status": "fail",
            "process_info": None,
            "message": process_info["error"],
            "code": "2"
        }
    logging.info(f"任务状态查询成功: {process_info}")
    return {
        "status": "success",
        "process_info": process_info,
        "message": "查询成功",
        "code": "0"
    }

def task_image_overlap(params):
    """图像切片重叠处理任务"""
    required_params = ['task_id', 'input', 'output', 'model_input_type']
    task_id = params['task_id']
    for param in required_params:
        print(f'param:{param}')
        if param not in params:
            return {
                "task_id": f"{task_id}",
                "status": "fail", 
                "message": f"缺少必要参数: {param}",
                "code": "1"
            }
    
    try:
        image_path = params['input']
        output_dir = params['output']
        model_input_type = params['model_input_type']
        overlap_ratio = 0.2
        try:
            num = 0
            result, num = slice_image_with_overlap(
                image_path=image_path,
                output_dir=output_dir,
                model_input_type=model_input_type,
                overlap_ratio=overlap_ratio
            )
        except Exception as e:
            error_msg = str(e)
            logging.error(f"任务失败: {task_id}, 错误={error_msg}")
        logging.info(f"切片任务处理完成")
        return {
            "task_id": f"{task_id}",
            "status": "success",
            "message": "图像切片处理完成",
            "json_file": f"{result}",
            "number": f"{num}",
            "code": "0"
        }
        
    except Exception as e:
        error_msg = str(e)
        logging.error(f"任务失败: {task_id}, 错误={error_msg}")
        return {
            "task_id": f"{task_id}",
            "status": "fail",
            "json_file": None,
            "number": None,
            "message": f"处理失败: {str(e)}",
            "code": "2"
        }
        
def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return None      
        
def task_run_distribute(params):
    """运行分布式YOLO任务（与task_run_script风格一致）"""
    required_params = ['distribute_path']
    for param in required_params:
        if param not in params:
            error_msg = f"分布式任务缺少必要参数: {param}"
            logging.error(error_msg)
            return {
                "status": "fail", 
                "pid": None,
                "message": f"缺少必要参数: {param}",
                "code": "1"
            }
    
    #port = find_available_port()
    script_path = params['distribute_path']
    args = params.get('args', [])
    task_id = params.get('task_id')
    device_id = params.get('device_id')
    startime = params.get('startime')
    '''
    if port is not None:
        args.extend(['--port', int(port)])
    '''
    logging.info(f"开始执行脚本任务: 脚本路径={script_path}, 参数={args}, 任务ID={task_id}")
    # 检查输入路径参数是否存在
    try:
        if '--input' in args:
            input_index = args.index('--input') + 1
            input_path = args[input_index]
            
            json_file = None
            if input_path and os.path.exists(input_path):
                if os.path.isfile(input_path):
                    input_dir = os.path.dirname(input_path)
                else:
                    input_dir = input_path
                
                # 查找JSON文件
                for f in os.listdir(input_dir):
                    if f.lower().endswith('.json'):
                        json_file = os.path.join(input_dir, f)
                        break
            
            # 如果找到JSON文件，添加到参数中
            if json_file:
                args.extend(['--json', json_file])
            else:
                error_msg = "未找到JSON配置文件"
                logging.error(error_msg)
                return {
                    "status": "fail", 
                    "pid": None,
                    "message": "参数错误",
                    "code": "1"
                }
    except Exception as e:
        error_msg = f"处理输入参数错误: {str(e)}"
        logging.error(error_msg)
        return {
            "status": "fail", 
            "pid": None,
            "message": "缺少有效的输入路径参数(--input)",
            "code": "1"
        }
    
    # 运行分布式脚本
    try:
        pid, _ = run_python_script_with_args(script_path, args, task_id, device_id, startime)
    except Exception as e:
        error_msg = f"运行脚本异常: {str(e)}"
        logging.error(error_msg)
        return {
            "status": "fail",
            "pid": None,
            "message": error_msg,
            "code": "2"
        }
    if pid is None:
        error_msg = "任务进程创建失败"
        logging.error(error_msg)
        return {
            "status": "fail",
            "pid": None,
            "message": "分布式任务进程创建失败",
            "code": "2"
        }
    
    time.sleep(1)
    status = check_process_status(pid)
    
    if status == 0:
        if running_processes[pid]['program'] == 'running':
            logging.info(f"分布式任务启动成功: PID={pid}, 任务ID={task_id}")
            return {
                "status": "success",
                "pid": pid,
                "id": task_id,
                "message": "分布式任务进程正在运行",
                "code": "0"
            }
        elif running_processes[pid]['program'] == 'completed':
            logging.info(f"分布式任务启动成功: PID={pid}, 任务ID={task_id}")
            return {
                "status": "success",
                "pid": pid,
                "id": task_id,
                "message": "分布式任务进程已完成",
                "code": "0"
            }
        else:
            error_msg = running_processes[pid].get('error', "任务进程创建失败或异常退出")
            logging.error(f"分布式任务失败: PID={pid}, 错误={error_msg}")
            return {
                "status": "fail",
                "pid": pid,
                "id": task_id,
                "message": "分布式任务进程创建失败或异常退出",
                "code": "2"
            }
    else:
        error_msg = running_processes[pid].get('error', "任务进程创建失败或异常退出")
        logging.error(f"分布式任务失败: PID={pid}, 错误={error_msg}")
        return {
            "status": "fail",
            "pid": pid,
            "id": task_id,
            "message": "分布式任务进程创建失败或异常退出",
            "code": "2"
        }
        
def task_run_pipeline(params):
    """运行分布式YOLO管道任务"""
    required_params = ['pipeline_path']
    for param in required_params:
        if param not in params:
            return {
                "status": "fail", 
                "pid": None,
                "message": f"缺少必要参数: {param}",
                "code": "1"
            }
    
    script_path = params['pipeline_path']
    args = params.get('args', [])
    startime = params.get('startime')
    task_id = params.get('task_id')
    device_id = params.get('device_id')
    
    try:
        if '--input' in args:

            input_index = args.index('--input') + 1
            input_path = args[input_index]
            
            json_file = None
            if input_path and os.path.exists(input_path):
                if os.path.isfile(input_path):
                    input_dir = os.path.dirname(input_path)
                else:
                    input_dir = input_path
                
                # 查找JSON文件
                for f in os.listdir(input_dir):
                    if f.lower().endswith('.json'):
                        json_file = os.path.join(input_dir, f)
                        break
            
            # 如果找到JSON文件，添加到参数中
            if json_file:
                args.extend(['--json', json_file])
            
    except Exception as e:
        error_msg = f"处理输入参数错误: {str(e)}"
        logging.error(error_msg)
        return {
            "status": "fail", 
            "pid": None,
            "message": "缺少有效的输入路径参数(--input)",
            "code": "1"
        }
    # 运行管道脚本
    pid, _ = run_python_script_with_args(script_path, args, task_id, device_id, startime)
    
    if pid is None:
        return {
            "status": "fail",
            "pid": None,
            "message": "分层任务进程创建失败",
            "code": "2"
        }
    
    time.sleep(1)
    status = check_process_status(pid)
    
    if status == 0:
        if running_processes[pid]['program'] == 'running':
            return {
                "status": "success",
                "pid": pid,
                "id": task_id,
                "message": "分层任务进程正在运行",
                "code": "0"
            }
        elif running_processes[pid]['program'] == 'completed':
            return {
                "status": "success",
                "pid": pid,
                "id": task_id,
                "message": "分层任务进程已完成",
                "code": "0"
            }
        else:
            return {
                "status": "fail",
                "pid": pid,
                "id": task_id,
                "message": "分层任务进程创建失败或异常退出",
                "code": "2"
            }
    else:
        return {
            "status": "fail",
            "pid": pid,
            "id": task_id,
            "message": "分层任务进程创建失败或异常退出",
            "code": "2"
        }

# 更新任务映射
TASK_MAPPING = {
    "run_script": task_run_script,
    "get_process": task_get_process,
    "run_overlap": task_image_overlap,
    "run_distribute": task_run_distribute,
    "run_pipeline": task_run_pipeline  # 添加新任务
}


class RequestHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        parsed_path = urlparse(self.path)
        
        if not parsed_path.path.startswith('/api/v1/docker/container/edtion/res/'):
            self.send_response(404)
            self.end_headers()
            return
            
        try:
            json_str = unquote(parsed_path.path.split('/api/v1/docker/container/edtion/res/')[1])
            json_data = json.loads(json_str)
            logging.info(f"收到请求: {self.path}")
        except (IndexError, json.JSONDecodeError) as e:
            error_msg = f"无效的JSON数据: {str(e)}"
            logging.error(error_msg)
            print(f'error:{e}')
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({
                "error": f"无效的JSON数据: {str(e)}",
                "success": False
            }).encode('utf-8'))
            return

        response = {'tasks': []}
        threads = []
        
        for key, value in json_data.items():
            if key in TASK_MAPPING:
                thread = threading.Thread(
                    target=lambda k=key, v=value: self._run_task(k, v, response),
                    daemon=True
                )
                threads.append(thread)
                thread.start()
        for thread in threads:
            thread.join()
            
        response_data = {
            "task": response['tasks'][0]["task"],
            "result": response['tasks'][0].get("result", {}),
        }
        log_request_response(json_data, response_data)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))

    def do_GET(self):
        parsed_path = urlparse(self.path)
        logging.info(f"收到请求: {self.path}")
        if parsed_path.path == '/api/v1/docker/container/edtion/res/get_port':
            try:
                
                # Find an available port
                port = find_available_port()
                
                # Prepare response
                response_data = {
                    "status": "success",
                    "port": port,
                    "message": "Available port found",
                    "code": "0"
                }
                log_request_response(self.path, response_data)
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response_data).encode('utf-8'))
                
            except Exception as e:
                error_response = {
                    "status": "fail",
                    "port": None,
                    "message": f"Failed to find available port: {str(e)}",
                    "code": "2"
                }
                log_request_response(self.path, error_response)
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(error_response).encode('utf-8'))
        else:
            error_msg = f"响应回复：无效的请求数据: {str(e)}"
            logging.error(error_msg)
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({
                "error": "Endpoint not found",
                "success": False
            }).encode('utf-8'))
            
    def _run_task(self, key, value, response):
        """执行任务并更新响应数据"""
        try:
            result = TASK_MAPPING[key](value)
            response['tasks'].append({
                "task": key,
                "result": result,
                "success": True
            })
        except Exception as e:
            error_msg = f"执行任务 {key} 失败: {str(e)}"
            logging.error(error_msg)
            response['tasks'].append({
                "task": key,
                "error": str(e),
                "success": False
            })

class ThreadedHTTPServer(ThreadingMixIn, HTTPServer):
    daemon_threads = True
    
if __name__ == '__main__':
    conda_init_path = "/opt/conda/etc/profile.d/conda.sh"
    conda_env_name = "ostrack"
    script_path= "/home/<USER>/service.py"
    base_cmd = f'source {conda_init_path} && conda activate {conda_env_name} && cd /home/<USER>/ && {sys.executable} {script_path}'
    cmd = ['bash', '-c', base_cmd]
    process = subprocess.Popen(cmd, env=os.environ.copy())
    server_address = ('', 8004)
    httpd = ThreadedHTTPServer(server_address, RequestHandler)
    print("服务器启动，访问 http://localhost:8004")
    httpd.serve_forever()
