#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据收集和存储模块 - 三线程系统实现数据收集、网络订阅和数据处理
使用ZeroMQ、SQLite和Watchdog实现高效的数据存储系统

创建时间: 2025-01-25
"""

import configparser
import json
import logging
import os
import queue
import re
import signal
import sqlite3
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

import zmq
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from database import DatabaseManager


class FileStabilityChecker:
    """文件稳定性检测器"""
    
    def __init__(self, check_interval: float = 0.05, max_retries: int = 3):
        self.check_interval = check_interval  # 50ms
        self.max_retries = max_retries
        self.logger = logging.getLogger('FileStabilityChecker')
    
    def is_file_stable(self, file_path: str) -> bool:
        """检测文件是否稳定（大小连续2次检查不变）"""
        if not os.path.exists(file_path):
            return False
        
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                # 第一次检查文件大小
                size1 = os.path.getsize(file_path)
                time.sleep(self.check_interval)
                
                # 第二次检查文件大小
                size2 = os.path.getsize(file_path)
                
                if size1 == size2:
                    self.logger.debug(f"文件稳定: {file_path} (大小: {size1} bytes)")
                    return True
                else:
                    retry_count += 1
                    self.logger.debug(f"文件大小变化: {file_path} ({size1} -> {size2}), 重试 {retry_count}/{self.max_retries}")
                    
            except (OSError, IOError) as e:
                self.logger.warning(f"检查文件稳定性时出错: {file_path}, {e}")
                retry_count += 1
                time.sleep(self.check_interval)
        
        self.logger.warning(f"文件稳定性检查失败: {file_path}, 已达到最大重试次数")
        return False


class FileEventHandler(FileSystemEventHandler):
    """文件系统事件处理器"""
    
    def __init__(self, message_queue: queue.Queue, zmq_publisher, stability_checker: FileStabilityChecker):
        super().__init__()
        self.message_queue = message_queue
        self.zmq_publisher = zmq_publisher
        self.stability_checker = stability_checker
        self.logger = logging.getLogger('FileEventHandler')
        
        # 文件命名模式: 
        self.file_pattern = re.compile(r'^(\d+)_([a-zA-Z0-9]+)_(\d+)\.json$')
    
    def on_created(self, event):
        """文件创建事件处理"""
        if event.is_directory:
            return
        
        file_path = event.src_path
        filename = os.path.basename(file_path)
        
        # 检查文件命名模式
        if not self.file_pattern.match(filename):
            self.logger.debug(f"文件命名不匹配模式，跳过: {filename}")
            return
        
        # 在后台线程中处理文件稳定性检查
        threading.Thread(
            target=self._process_file_async,
            args=(file_path, filename),
            daemon=True
        ).start()
    
    def _process_file_async(self, file_path: str, filename: str):
        """异步处理文件"""
        try:
            # 检查文件稳定性
            if not self.stability_checker.is_file_stable(file_path):
                self.logger.warning(f"文件不稳定，跳过处理: {file_path}")
                return
            self.logger.info(f"检测到新文件: {file_path}")
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 解析文件名获取数据类型
            match = self.file_pattern.match(filename)
            if match:
                file_type = str(match.group(2))
                topic = 'original_data' if file_type == "0" else 'feature_data'
                
                # 发布到ZeroMQ
                self._publish_to_zmq(topic, filename, file_content)
                
                # 添加到消息队列
                message = {
                    'source': 'file_monitor',
                    'topic': topic,
                    'filename': filename,
                    'content': file_content,
                    'timestamp': time.time()
                }
                self.message_queue.put(message)
                
                self.logger.info(f"文件处理完成: {filename}, 类型: {topic}")
            
        except Exception as e:
            self.logger.error(f"处理文件时出错: {file_path}, {e}")
    
    def _publish_to_zmq(self, topic: str, filename: str, content: bytes):
        """发布消息到ZeroMQ"""
        try:
            message_data = {
                'topic': topic,
                'filename': filename,
                'content': content.decode('utf-8', errors='ignore')
            }
            
            # 发送多部分消息: [topic, json_data]
            self.zmq_publisher.send_multipart([
                topic.encode('utf-8'),
                json.dumps(message_data).encode('utf-8')
            ])
            
            self.logger.debug(f"ZeroMQ消息发布成功: {topic} - {filename}")
            
        except Exception as e:
            self.logger.error(f"ZeroMQ消息发布失败: {e}")


class FileMonitorProducer:
    """生产者线程1: 文件监控器"""
    
    def __init__(self, config: Dict[str, Any], message_queue: queue.Queue):
        self.config = config
        self.message_queue = message_queue
        self.logger = logging.getLogger('FileMonitorProducer')
        self.running = False
        
        # 初始化组件
        self.stability_checker = FileStabilityChecker()
        self.observer = Observer()
        self.zmq_context = zmq.Context()
        self.zmq_publisher = None
        
        # 配置参数
        self.watch_directories = self.config.get('watch_directories', '').split(';')
        self.zmq_publish_addr = self.config.get('zmq_publish_addr', 'tcp://0.0.0.0:13232')
    
    def start(self):
        """启动文件监控"""
        observer_started = False
        try:
            self.running = True

            # 初始化ZeroMQ发布者，添加重试机制
            self.zmq_publisher = self.zmq_context.socket(zmq.PUB)
            try:
                self.zmq_publisher.bind(self.zmq_publish_addr)
                self.logger.info(f"ZeroMQ发布者绑定成功: {self.zmq_publish_addr}")
            except zmq.ZMQError as e:
                if "Address already in use" in str(e):
                    # 尝试使用随机端口
                    import random
                    for _ in range(5):  # 最多尝试5次
                        try:
                            random_port = random.randint(13233, 13299)
                            alt_addr = f"tcp://0.0.0.0:{random_port}"
                            self.zmq_publisher.bind(alt_addr)
                            self.logger.warning(f"原端口被占用，使用替代端口: {alt_addr}")
                            break
                        except zmq.ZMQError:
                            continue
                    else:
                        raise Exception("无法找到可用端口绑定ZeroMQ发布者")
                else:
                    raise

            # 创建事件处理器
            event_handler = FileEventHandler(
                self.message_queue,
                self.zmq_publisher,
                self.stability_checker
            )

            # 监控指定目录
            directories_added = 0
            for directory in self.watch_directories:
                if directory.strip() and os.path.exists(directory.strip()):
                    self.observer.schedule(event_handler, directory.strip(), recursive=True)
                    self.logger.info(f"开始监控目录: {directory.strip()}")
                    directories_added += 1
                else:
                    self.logger.warning(f"监控目录不存在: {directory}")

            if directories_added > 0:
                self.observer.start()
                observer_started = True
                self.logger.info("文件监控器启动成功")

                # 保持运行
                while self.running:
                    time.sleep(1)
            else:
                self.logger.error("没有有效的监控目录，文件监控器无法启动")

        except Exception as e:
            self.logger.error(f"文件监控器启动失败: {e}")
        finally:
            self._cleanup(observer_started)

    def stop(self):
        """停止文件监控"""
        self.running = False
        self._cleanup(True)

    def _cleanup(self, observer_started: bool):
        """清理资源"""
        try:
            if self.observer and observer_started:
                self.observer.stop()
                self.observer.join()

            if self.zmq_publisher:
                self.zmq_publisher.close()

            if self.zmq_context:
                self.zmq_context.term()

            self.logger.info("文件监控器已停止")
        except Exception as e:
            self.logger.error(f"清理文件监控器资源时出错: {e}")


class NetworkSubscriberProducer:
    """生产者线程2: 网络订阅器"""
    
    def __init__(self, config: Dict[str, Any], message_queue: queue.Queue):
        self.config = config
        self.message_queue = message_queue
        self.logger = logging.getLogger('NetworkSubscriberProducer')
        self.running = False
        
        # ZeroMQ配置
        self.zmq_context = zmq.Context()
        self.subscribers = []
        
        # 配置参数
        cluster_nodes = self.config.get('cluster_nodes', '').split(',')
        self.connect_addresses = [f"tcp://{node.strip()}:13232" for node in cluster_nodes if node.strip()]
        self.subscribe_topics = self.config.get('subscribe_topics', 'original_data,feature_data').split(',')
    
    def start(self):
        """启动网络订阅"""
        try:
            self.running = True

            # 为每个地址创建订阅者
            for addr in self.connect_addresses:
                try:
                    subscriber = self.zmq_context.socket(zmq.SUB)
                    # 设置连接超时
                    subscriber.setsockopt(zmq.RCVTIMEO, 1000)  # 1秒接收超时
                    subscriber.setsockopt(zmq.RCVHWM, 20000)
                    subscriber.connect(addr)

                    # 订阅指定主题
                    for topic in self.subscribe_topics:
                        subscriber.setsockopt(zmq.SUBSCRIBE, topic.strip().encode('utf-8'))

                    self.subscribers.append(subscriber)
                    self.logger.info(f"连接到集群节点: {addr}")

                except Exception as e:
                    self.logger.warning(f"连接集群节点失败: {addr}, {e}")

            if not self.subscribers:
                self.logger.warning("没有可用的集群节点连接，网络订阅器将继续运行但不会接收消息")

            # 使用轮询器处理多个订阅者
            poller = zmq.Poller()
            for subscriber in self.subscribers:
                poller.register(subscriber, zmq.POLLIN)

            self.logger.info("网络订阅器启动成功")

            # 接收消息循环
            while self.running:
                try:
                    if self.subscribers:
                        socks = dict(poller.poll(timeout=1000))  # 1秒超时

                        for subscriber in self.subscribers:
                            if subscriber in socks and socks[subscriber] == zmq.POLLIN:
                                self._handle_received_message(subscriber)
                    else:
                        # 没有订阅者时简单等待
                        time.sleep(1)

                except zmq.Again:
                    continue  # 超时，继续循环
                except Exception as e:
                    self.logger.error(f"接收消息时出错: {e}")
                    time.sleep(1)

        except Exception as e:
            self.logger.error(f"网络订阅器启动失败: {e}")
        finally:
            self.stop()
    
    def _handle_received_message(self, subscriber):
        """处理接收到的消息"""
        try:
            # 接收多部分消息
            topic, message_data = subscriber.recv_multipart(zmq.NOBLOCK)
            topic = topic.decode('utf-8')
            
            # 解析消息数据
            data = json.loads(message_data.decode('utf-8'))
            filename = data.get('filename', 'unknown')
            # 移除路径部分，只保留文件名
            safe_filename = os.path.basename(filename)
            content = data.get('content', '')
            # 根据主题进行不同处理
            if topic in ['original_data', 'feature_data']:
                message = {
                    'source': 'network_subscriber',
                    'topic': topic,
                    'filename': safe_filename,
                    'content': content.encode('utf-8'),
                    'timestamp': time.time()
                }
                
                self.message_queue.put(message)
                storage_dir = f"/home/<USER>/resultData/{topic}"
                os.makedirs(storage_dir, exist_ok=True)
                file_path = os.path.join(storage_dir, safe_filename)
                content_str = data.get('content', '')
                content_bytes = content_str.encode('utf-8') if isinstance(content_str, str) else content_str
                os.makedirs(storage_dir, exist_ok=True)
                with open(file_path, 'wb') as f:
                    f.write(content_bytes)
                self.logger.debug(f"接收到网络消息: {topic} - {data.get('filename', 'unknown')}")
                self.logger.info(f"成功保存文件: {file_path}")
        except zmq.Again:
            pass  # 没有消息可接收
        except Exception as e:
            self.logger.error(f"处理接收消息时出错: {e}")
    
    def stop(self):
        """停止网络订阅"""
        self.running = False
        
        for subscriber in self.subscribers:
            subscriber.close()
        
        if self.zmq_context:
            self.zmq_context.term()
        
        self.logger.info("网络订阅器已停止")


class DataProcessorConsumer:
    """消费者线程: 数据处理器"""

    def __init__(self, config: Dict[str, Any], message_queue: queue.Queue):
        self.config = config
        self.message_queue = message_queue
        self.logger = logging.getLogger('DataProcessorConsumer')
        self.running = False

        # 线程池配置
        self.max_workers = int(self.config.get('max_workers', 4))
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)

        # 数据库管理器
        self.db_manager = DatabaseManager()
        self.db_manager._initialize_database()
        self.connection_pool = self.db_manager.connection_pool  
    def start(self):
        """启动数据处理器"""
        try:
            self.running = True
            self.logger.info("数据处理器启动成功")

            while self.running:
                try:
                    # 从队列获取消息，设置超时避免阻塞
                    message = self.message_queue.get(timeout=1)

                    # 提交到线程池处理
                    self.thread_pool.submit(self._process_message, message)

                    # 标记任务完成
                    self.message_queue.task_done()

                except queue.Empty:
                    continue  # 队列为空，继续循环
                except Exception as e:
                    self.logger.error(f"处理消息时出错: {e}")

        except Exception as e:
            self.logger.error(f"数据处理器启动失败: {e}")
        finally:
            self.stop()

    def _process_message(self, message: Dict[str, Any]):
        """处理单个消息"""
        try:
            source = message.get('source', 'unknown')
            topic = message.get('topic', '')
            filename = message.get('filename', '')
            content = message.get('content', b'')

            self.logger.debug(f"处理消息: {source} - {topic} - {filename}")

            # 解析任务信息 - 使用database.py的逻辑
            task_info = self._parse_filename(filename)
            if not task_info:
                self.logger.debug(f"临时文件，跳过解析存储: {filename}")
                return

             # 解析并存储数据 - 使用database.py的逻辑
            self._parse_and_store_data(task_info, content, filename)

        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            
    def _parse_filename(self, filename: str) -> Dict[str, str]:
        """
        解析文件名格式：卫星id_任务类型_时间戳.ext - 

        :param filename: 文件名
        :return: 解析后的任务信息字典
        """
        try:
            base_name = os.path.splitext(filename)[0]
            file_ext = os.path.splitext(filename)[1].lower()
            parts = base_name.split('_')

            if len(parts) < 3:
                self.logger.debug("文件名解析完成，不满足三部分要求")
                return None

            self.logger.debug(f"文件名解析成功，sat_id={parts[0]},task_info={parts[1]},timestamp={parts[2]}")
            sat_id = parts[0]
            task_category = parts[1]
            timestamp = parts[2]
                         
            return {
                'filename': filename,
                'sat_id': sat_id,
                'task_category': str(task_category),
                'timestamp': timestamp,
                'file_ext': file_ext
            }

        except Exception as e:
            self.logger.error(f"解析文件名失败: {filename} - {e}")
            return None

    def _parse_and_store_data(self, task_info: dict, content: bytes, file_path: str):
        """解析并存储任务数据 - """
        try:
            if task_info['task_category'] == '0' and task_info['file_ext'] == '.json':
                self._store_origin_data(content, task_info)
            elif task_info['task_category'] == 'BHSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_BHSF(content, task_info)
            elif task_info['task_category'] == 'WZZH' and task_info['file_ext'] == '.json':
                self._store_feature_data_WZZH(content, task_info)
            elif task_info['task_category'] == 'RHDW' and task_info['file_ext'] == '.json':
                self._store_feature_data_RHDW(content, task_info)
            elif task_info['task_category'] == 'XDSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_XDSF(content, task_info)
            elif task_info['task_category'] == 'BKSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_BKSF(content, task_info)
            elif task_info['task_category'] == 'FXJG' and task_info['file_ext'] == '.json':
                self._store_decision_data(content, task_info)
            else:
                self.logger.warning(f"未知的任务类别或文件类型: {task_info['task_category']} {task_info['file_ext']}")
        except Exception as e:
            self.logger.error(f"任务数据存储失败: {file_path} - {e}")

    def _store_origin_data(self, content: bytes, task_info: Dict[str, str]):
        """
        存储源数据到origional_table和truth_table表 - 

        :param content: 文件内容
        :param task_info: 任务信息
        """
        try:
            filename = task_info['filename']
            # 解析JSON内容
            json_data = json.loads(content.decode('utf-8'))

            device_info = json_data.get('deviceInfo', {})
            selected_image = json_data.get('selectedImage', {})

            # 根据task.txt中的映射关系进行类型转换
            device_type_map = {0: 'GEO', 131072: 'LEO', 131073: 'LEO_SAR', 131074: 'LEO_RADAR', 131075: 'LEO_ELECTRO'}
            equip_type_map = {1: 'CCD', 2: 'IR', 3: 'SAR', 4: 'RADAR', 5: 'ELECTRO', 6: 'PAN'}
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP',
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }

            # 提取基本信息用于origional_table
            origin_data = {
                'sat_id': str(task_info.get('sat_id', '')),
                'sat_category': device_type_map.get(device_info.get('deviceType', 0), 'OTHER'),
                'sat_load_category': equip_type_map.get(device_info.get('equipType', 0), 'OTHER'),
                'planed_start_time': str(device_info.get('planedStartTime', 0)),
                'planed_end_time': str(device_info.get('planedEndTime', 0)),
                'timestamp': str(task_info.get('timestamp', 0)),

                # 文件相关信息
                'file_size': selected_image.get('imageSize', 0.0),
                'file_name': selected_image.get('imageName', filename),
                'file_path': selected_image.get('imagePath', ''),
                'image_truth_width': selected_image.get('image_truth_width', 0.0),
                'image_resolution': selected_image.get('image_resolution', 0.0),

                # 视线中心点ENU坐标系信息
                'enu_base_longitude': selected_image.get('enuBaseLon', 0.0),
                'enu_base_latitude': selected_image.get('enuBaseLat', 0.0),
                'enu_base_altitude': selected_image.get('enuBaseAlt', 0.0),

                # 卫星位置信息
                'sat_enu_x': selected_image.get('satPosEnuX', 0.0),
                'sat_enu_y': selected_image.get('satPosEnuY', 0.0),
                'sat_enu_z': selected_image.get('satPosEnuZ', 0.0),

                # 相机四元数信息
                'cam_enu_x': selected_image.get('camQENU_X', 0.0),
                'cam_enu_y': selected_image.get('camQENU_Y', 0.0),
                'cam_enu_z': selected_image.get('camQENU_Z', 0.0),
                'cam_enu_w': selected_image.get('camQENU_W', 0.0),

                # J2000坐标系信息
                'sat_j2000_x': selected_image.get('satPosJ2000X', 0.0),
                'sat_j2000_y': selected_image.get('satPosJ2000Y', 0.0),
                'sat_j2000_z': selected_image.get('satPosJ2000Z', 0.0),

                # 四元数信息
                'sat_qbi_x': selected_image.get('satQbiX', 0.0),
                'sat_qbi_y': selected_image.get('satQbiY', 0.0),
                'sat_qbi_z': selected_image.get('satQbiZ', 0.0),
                'sat_qbi_w': selected_image.get('satQbiW', 0.0),

                # 载荷视场角
                'load_fov_x': selected_image.get('fovX', 0.0),
                'load_fov_y': selected_image.get('fovY', 0.0),

                # 图像像素信息
                'image_pixel_x': selected_image.get('camPixelX', 0.0),
                'image_pixel_y': selected_image.get('camPixelY', 0.0)
            }

            # 插入origional_table数据
            origin_record_id = self._insert_origin_data(origin_data)
            if origin_record_id:
                self.logger.info(f"成功插入源数据，记录ID: {origin_record_id}")
            else:
                self.logger.error(f"插入源数据失败: {filename}")
                return

            # 处理目标信息，插入truth_table
            target_info_list = selected_image.get('targetInfo', [])
            target_num = selected_image.get('targetNum', 0)

            if target_num > 0 and target_info_list:
                for i, target_info in enumerate(target_info_list[:target_num]):
                    # 提取目标信息
                    returnbox = target_info.get('returnbox', {})

                    truth_data = {
                        'sat_id': str(task_info.get('sat_id', '')),
                        'timestamp': str(task_info.get('timestamp', 0)),
                        'target_id': str(target_info.get('targetID', '')),
                        'fleet_number': str(target_info.get('targetFleetNumber', '')),
                        'target_category': target_type_map.get(target_info.get('targetType', 0), 'OTHER'),
                        'target_direction': str(target_info.get('targetDirection', 0.0)),

                        # 目标位置信息
                        'target_longitude': target_info.get('targetPosLon', 0.0),
                        'target_latitude': target_info.get('targetPosLat', 0.0),
                        'target_altitude': target_info.get('targetPosAlt', 0.0),

                        # 卫星坐标系下的目标位置
                        'target_sat_x': target_info.get('targetPosInSatX', 0.0),
                        'target_sat_y': target_info.get('targetPosInSatY', 0.0),
                        'target_sat_z': target_info.get('targetPosInSatZ', 0.0),

                        # 目标像素位置信息
                        'target_x': returnbox.get('x', 0.0),
                        'target_y': returnbox.get('y', 0.0),
                        'target_width': returnbox.get('width', 0.0),
                        'target_height': returnbox.get('height', 0.0)
                    }

                    # 插入truth_table数据
                    truth_record_id = self._insert_truth_data(truth_data)
                    if truth_record_id:
                        self.logger.debug(f"成功插入目标数据 {i+1}/{target_num}，记录ID: {truth_record_id}")
                    else:
                        self.logger.error(f"插入目标数据失败: {filename}, 目标 {i+1}")

                self.logger.info(f"处理了 {len(target_info_list[:target_num])} 个目标信息")
            else:
                self.logger.info(f"文件 {filename} 中没有目标信息")

            self.logger.info(f"源数据已成功存储到数据库: {filename}")

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败 {filename}: {e}")
        except Exception as e:
            self.logger.error(f"存储源数据失败 {filename}: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _insert_origin_data(self, origin_data: Dict[str, Any]) -> Optional[int]:
        """插入原始数据到origional_table"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 构建SQL语句
                columns = ', '.join(origin_data.keys())
                placeholders = ', '.join([f':{key}' for key in origin_data.keys()])
                sql = f"INSERT INTO origional_table ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, origin_data)
                return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"插入原始数据失败: {e}")
            return None

    def _insert_truth_data(self, truth_data: Dict[str, Any]) -> Optional[int]:
        """插入真值数据到truth_table"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 构建SQL语句
                columns = ', '.join(truth_data.keys())
                placeholders = ', '.join([f':{key}' for key in truth_data.keys()])
                sql = f"INSERT INTO truth_table ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, truth_data)
                return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"插入真值数据失败: {e}")
            return None

    def _insert_feature_data(self, feature_data: Dict[str, Any]) -> Optional[int]:
        """插入特征数据到feature_table"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 构建SQL语句
                columns = ', '.join(feature_data.keys())
                placeholders = ', '.join([f':{key}' for key in feature_data.keys()])
                sql = f"INSERT INTO feature_table ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, feature_data)
                return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"插入特征数据失败: {e}")
            return None

    def _store_feature_data_BHSF(self, content: bytes, task_info: Dict[str, str]):
        """
        存储特征数据到feature_table表 - 

        :param content: 文件内容
        :param task_info: 任务信息
        """
        try:
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP',
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))
            for data_item in json_data:
                target_image_path = data_item.get('image_path','')
                target_id = data_item.get('target_id')
                target_category = data_item.get('type')
                target_truth = data_item.get('truth')
                position = data_item.get('position', {})
                target_x = position.get('x1', 0)
                target_y = position.get('y1', 0)
                target_width = position.get('width', 0)
                target_height = position.get('height', 0)

                feature_data = {
                    'sat_id': task_info['sat_id'],
                    'fleet_number': 0,
                    'timestamp': task_info['timestamp'],
                    'target_category': target_type_map.get(target_category, 'OTHER'),
                    'target_id': target_id,
                    'target_image_path': target_image_path,
                    'target_truth': target_truth,
                    'target_x': target_x,
                    'target_y': target_y,
                    'target_width': target_width,
                    'target_height': target_height
                }

                record_id = self._insert_feature_data(feature_data)
                if record_id:
                    self.logger.info(f"身份识别数据存储成功: target_id={feature_data['target_id']}, "
                                    f"type={feature_data['target_category']}, truth={feature_data['target_truth']} (ID: {record_id})")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析JSON特征数据失败: {filename} - {e}")

    def _store_feature_data_WZZH(self, content: bytes, task_info: Dict[str, str]):
        """存储位置转换特征数据 - """
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for target_data in json_data.get('targets', []):
                target_id = target_data.get('target_id')
                target_longitude = target_data.get('longitude')
                target_latitude = target_data.get('latitude')

                # 更新feature_table中对应记录的经纬度信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_longitude = ?, target_latitude = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        target_longitude, target_latitude,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"位置转换数据更新成功: target_id={target_id}, "
                                        f"lon={target_longitude}, lat={target_latitude}")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行位置更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析位置转换数据失败: {filename} - {e}")

    def _store_feature_data_RHDW(self, content: bytes, task_info: Dict[str, str]):
        """存储融合定位特征数据 - """
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for observation in json_data.get('observations', []):
                target_id = observation.get('target_id')
                fusion_longitude = observation.get('lon')
                fusion_latitude = observation.get('lat')

                # 更新feature_table中对应记录的融合定位信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_fusion_longitude = ?, target_fusion_latitude = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        fusion_longitude, fusion_latitude,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"融合定位数据更新成功: target_id={target_id}, "
                                        f"fusion_lon={fusion_longitude}, fusion_lat={fusion_latitude}")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行融合定位更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析融合定位数据失败: {filename} - {e}")

    def _store_feature_data_XDSF(self, content: bytes, task_info: Dict[str, str]):
        """存储电侦智能特征数据 - """
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for target_data in json_data.get('targets', []):
                target_id = target_data.get('target_id')
                target_npy_path = target_data.get('npy_path')

                # 更新feature_table中对应记录的npy路径信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_npy_path = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        target_npy_path,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"雷达智能数据更新成功: target_id={target_id}, npy_path={target_npy_path}")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行雷达智能更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析雷达智能数据失败: {filename} - {e}")

    def _store_feature_data_BKSF(self, content: bytes, task_info: Dict[str, str]):
        """存储雷达智能特征数据 - """
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for target_data in json_data.get('targets', []):
                target_id = target_data.get('target_id')
                longitude_speed = target_data.get('longitude_speed')
                latitude_speed = target_data.get('latitude_speed')
                total_speed = target_data.get('total_speed')

                # 更新feature_table中对应记录的速度信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_longitude_speed = ?, target_latitude_speed = ?, target_total_speed = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        longitude_speed, latitude_speed, total_speed,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"电侦智能数据更新成功: target_id={target_id}, "
                                        f"speeds=({longitude_speed}, {latitude_speed}, {total_speed})")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行电侦智能更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析电侦智能数据失败: {filename} - {e}")

    def _store_decision_data(self, content: bytes, task_info: Dict[str, str]):
        """存储决策数据到decision_table表 - """
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            query_days = json_data.get('query_days', '')

            # 处理每个舰队的数据
            for fleet_key in ['FLEET_01', 'FLEET_02', 'FLEET_03']:
                fleet_data = json_data.get(fleet_key, {})
                if fleet_data.get('success', False):
                    speed_threat = fleet_data.get('speed_threat', 0.0)
                    heading_threat = fleet_data.get('heading_threat', 0.0)
                    distance_threat = fleet_data.get('distance_threat', 0.0)
                    heading_angle = fleet_data.get('heading_angle', 0.0)
                    speed = fleet_data.get('speed', 0.0)
                    lat = fleet_data.get('lat', 0.0)
                    lon = fleet_data.get('lon', 0.0)
                    intention = fleet_data.get('intention', '')

                    decision_data = {
                        'timestamp': task_info['timestamp'],
                        'query_days': query_days,
                        'fleet_number': fleet_key,
                        'speed_threat': speed_threat,
                        'heading_threat': heading_threat,
                        'distance_threat': distance_threat,
                        'heading_angle': heading_angle,
                        'speed': speed,
                        'lat': lat,
                        'lon': lon,
                        'intention': intention
                    }

                    # 插入决策数据
                    with self.connection_pool.get_connection() as conn:
                        cursor = conn.cursor()

                        columns = ', '.join(decision_data.keys())
                        placeholders = ', '.join([f':{key}' for key in decision_data.keys()])
                        sql = f"INSERT INTO decision_table ({columns}) VALUES ({placeholders})"

                        cursor.execute(sql, decision_data)
                        record_id = cursor.lastrowid

                        if record_id:
                            self.logger.info(f"决策数据存储成功: {fleet_key}, intention={intention} (ID: {record_id})")
                        else:
                            self.logger.error(f"决策数据存储失败: {fleet_key}")
                else:
                    err = fleet_data.get('error', '未知错误')
                    self.logger.warning(f"决策数据处理: {fleet_key}，错误信息: {err}")
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析决策数据失败: {filename} - {e}")

    def stop(self):
        """停止数据处理器"""
        self.running = False

        # 关闭线程池
        self.thread_pool.shutdown(wait=True)

        # 关闭数据库连接
        if self.db_manager:
            self.db_manager.close()

        self.logger.info("数据处理器已停止")


class StoreService:
    """分布式数据存储服务主类"""

    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = self._load_config()

        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger('StoreService')

        # 服务状态
        self.running = False
        self.threads = []

        # 消息队列
        queue_maxsize = int(self.config.get('queue_maxsize', 1000))
        self.message_queue = queue.Queue(maxsize=queue_maxsize)

        # 初始化组件
        self.file_monitor = FileMonitorProducer(self.config, self.message_queue)
        self.network_subscriber = NetworkSubscriberProducer(self.config, self.message_queue)
        self.data_processor = DataProcessorConsumer(self.config, self.message_queue)

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(self.config_path, encoding='utf-8')

        # 返回store部分的配置
        if 'store' in config:
            return dict(config['store'])
        else:
            self.logger.warning("配置文件中未找到[store]部分，使用默认配置")
            return {}

    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('store.log', encoding='utf-8')
            ]
        )

    def start(self):
        """启动存储服务"""
        try:
            if not self.config.get('service_enabled', 'true').lower() == 'true':
                self.logger.info("存储服务已禁用")
                return

            self.running = True
            self.logger.info("启动分布式数据存储服务...")

            # 启动生产者线程1: 文件监控
            file_monitor_thread = threading.Thread(
                target=self.file_monitor.start,
                name="FileMonitorThread",
                daemon=True
            )
            file_monitor_thread.start()
            self.threads.append(file_monitor_thread)

            # 启动生产者线程2: 网络订阅
            network_subscriber_thread = threading.Thread(
                target=self.network_subscriber.start,
                name="NetworkSubscriberThread",
                daemon=True
            )
            network_subscriber_thread.start()
            self.threads.append(network_subscriber_thread)

            # 启动消费者线程: 数据处理
            data_processor_thread = threading.Thread(
                target=self.data_processor.start,
                name="DataProcessorThread",
                daemon=True
            )
            data_processor_thread.start()
            self.threads.append(data_processor_thread)

            self.logger.info("所有线程启动完成")

            # 主线程保持运行
            try:
                while self.running:
                    time.sleep(1)

                    # 检查线程状态
                    for thread in self.threads:
                        if not thread.is_alive():
                            self.logger.warning(f"线程 {thread.name} 已停止")

            except KeyboardInterrupt:
                self.logger.info("接收到中断信号，正在停止服务...")
                self.stop()

        except Exception as e:
            self.logger.error(f"启动存储服务失败: {e}")
            self.stop()

    def stop(self):
        """停止存储服务"""
        self.logger.info("正在停止分布式数据存储服务...")
        self.running = False

        # 停止各个组件
        self.file_monitor.stop()
        self.network_subscriber.stop()
        self.data_processor.stop()

        # 等待线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=5)

        self.logger.info("分布式数据存储服务已停止")

    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'running': self.running,
            'queue_size': self.message_queue.qsize(),
            'threads': [
                {
                    'name': thread.name,
                    'alive': thread.is_alive()
                }
                for thread in self.threads
            ]
        }


def signal_handler(signum, frame):
    """信号处理器"""
    # frame parameter is required by signal handler interface but not used
    _ = frame  # Suppress unused variable warning
    print(f"\n接收到信号 {signum}，正在停止服务...")
    if 'store_service' in globals():
        store_service.stop()
    sys.exit(0)


def main():
    """主函数"""
    global store_service

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建并启动存储服务
    store_service = StoreService()
    store_service.start()


if __name__ == "__main__":
    main()
