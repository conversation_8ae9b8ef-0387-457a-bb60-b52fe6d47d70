#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务管理脚本 - 同时启动搜索服务和存储服务
支持指定日志输出目录和配置文件路径

创建时间: 2025-01-25
"""
import argparse
import asyncio
import logging
import os
import signal
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Optional

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('ServiceManager')


class ServiceManager:
    """服务管理器 - 同时管理搜索服务和存储服务"""
    
    def __init__(self, log_dir: str, config_path: str = "config.ini"):
        self.log_dir = Path(log_dir)
        self.config_path = config_path
        self.processes = []
        self.running = False
        
        # 确保日志目录存在
        self.log_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"日志目录: {self.log_dir.absolute()}")
    
    def start_services(self):
        """启动所有服务"""
        try:
            self.running = True
            logger.info("正在启动服务...")
            
            # 启动搜索服务
            search_log = self.log_dir / "search.log"
            search_cmd = [
                sys.executable, "search.py",
                "--config", self.config_path
            ]
            search_process = self._start_process("搜索服务", search_cmd, search_log)
            self.processes.append(search_process)
            
            # 等待一下确保搜索服务完全启动
            time.sleep(2)
            
            # 启动存储服务
            store_log = self.log_dir / "store.log"
            store_cmd = [
                sys.executable, "store.py",
                "--config", self.config_path
            ]
            store_process = self._start_process("存储服务", store_cmd, store_log)
            self.processes.append(store_process)
            
            logger.info("所有服务已启动")
            logger.info(f"搜索服务日志: {search_log}")
            logger.info(f"存储服务日志: {store_log}")
            
            # 等待所有进程结束
            self._wait_for_processes()
            
        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            self.stop_services()
    
    def _start_process(self, name: str, cmd: List[str], log_file: Path):
        """启动一个子进程并重定向输出到日志文件"""
        try:
            logger.info(f"启动{name}: {' '.join(cmd)}")
            
            # 打开日志文件
            with open(log_file, 'w', encoding='utf-8') as log_handle:
                # 启动进程并重定向输出
                process = subprocess.Popen(
                    cmd,
                    stdout=log_handle,
                    stderr=subprocess.STDOUT,
                    text=True,
                    encoding='utf-8'
                )
            
            logger.info(f"{name}已启动，PID: {process.pid}")
            return process
            
        except Exception as e:
            logger.error(f"启动{name}失败: {e}")
            raise
    
    def _wait_for_processes(self):
        """等待所有进程结束"""
        try:
            while self.running and any(p.poll() is None for p in self.processes):
                # # 检查进程状态
                # for i, process in enumerate(self.processes):
                #     if process.poll() is not None:
                #         logger.warning(f"进程 {i} 已退出，返回码: {process.returncode}")
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在停止服务...")
            self.stop_services()
    
    def stop_services(self):
        """停止所有服务"""
        self.running = False
        logger.info("正在停止服务...")
        
        for i, process in enumerate(self.processes):
            if process.poll() is None:  # 进程仍在运行
                try:
                    logger.info(f"终止进程 {i} (PID: {process.pid})")
                    process.terminate()  # 优雅终止
                    
                    # 等待一段时间让进程自行退出
                    time.sleep(2)
                    
                    if process.poll() is None:  # 如果仍未退出，强制终止
                        logger.warning(f"强制终止进程 {i} (PID: {process.pid})")
                        process.kill()
                        
                except Exception as e:
                    logger.error(f"终止进程 {i} 失败: {e}")
        
        self.processes.clear()
        logger.info("所有服务已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分布式文件处理系统服务管理器")
    parser.add_argument(
        "--log-dir", 
        default="./logs",
        help="日志输出目录，默认为 ./logs"
    )
    parser.add_argument(
        "--config", 
        default="config.ini",
        help="配置文件路径，默认为 config.ini"
    )
    
    args = parser.parse_args()
    
    # 创建服务管理器
    manager = ServiceManager(args.log_dir, args.config)
    
    # 设置信号处理
    def signal_handler(sig, frame):
        logger.info(f"接收到信号 {sig}，正在停止服务...")
        manager.stop_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务
    try:
        manager.start_services()
    except Exception as e:
        logger.error(f"服务运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()