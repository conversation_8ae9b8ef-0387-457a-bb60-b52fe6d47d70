#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os, csv, json, base64

def _ensure_dir(path: str):
    os.makedirs(path, exist_ok=True)

def _serialize_value(v):
    # 处理可能出现的 BLOB / bytes，转成 base64 字符串，避免写文件报错
    if isinstance(v, (bytes, bytearray)):
        return {"__base64__": True, "data": base64.b64encode(v).decode("utf-8")}
    return v

def export_table_to_csv(cursor, table_name: str, out_dir: str, chunk_size: int = 1000):
    _ensure_dir(out_dir)
    csv_path = os.path.join(out_dir, f"{table_name}.csv")

    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [c[1] for c in cursor.fetchall()]

    cursor.execute(f"SELECT * FROM {table_name}")
    with open(csv_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(columns)
        while True:
            rows = cursor.fetchmany(chunk_size)
            if not rows:
                break
            # CSV 对 bytes 做 str() 转换，避免写不进去
            for row in rows:
                writer.writerow([str(v) if isinstance(v, (bytes, bytearray)) else v for v in row])
    return csv_path

def export_table_to_jsonl(cursor, table_name: str, out_dir: str, chunk_size: int = 1000):
    _ensure_dir(out_dir)
    jsonl_path = os.path.join(out_dir, f"{table_name}.jsonl")

    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [c[1] for c in cursor.fetchall()]

    cursor.execute(f"SELECT * FROM {table_name}")
    with open(jsonl_path, "w", encoding="utf-8") as f:
        while True:
            rows = cursor.fetchmany(chunk_size)
            if not rows:
                break
            for row in rows:
                obj = {col: _serialize_value(val) for col, val in zip(columns, row)}
                f.write(json.dumps(obj, ensure_ascii=False) + "\n")
    return jsonl_path

def printTable(path, preview_rows: int = 10, export_dir: str = "./exports"):
    conn = sqlite3.connect(path)
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
    tables = cursor.fetchall()

    if not tables:
        print(f"数据库 {path} 中没有用户表。")
        conn.close()
        return

    print(f"数据库 {path} 中的表：")
    for (table_name,) in tables:
        print(f"\n{'='*60}")
        print(f"Table: {table_name}")
        print(f"{'='*60}")

        # 列信息
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        print("\nColumns:")
        for i, column in enumerate(columns):
            print(f"    {i+1}. {column[1]} ({column[2]}) {'NOT NULL' if column[3] else 'NULL'}")

        # 行数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = cursor.fetchone()[0]
        print(f"\nTotal rows: {row_count}")

        # 预览
        if row_count > 0:
            print(f"\nTable Content (showing first {preview_rows} rows):")
            print("-" * 80)
            cursor.execute(f"SELECT * FROM {table_name} LIMIT {preview_rows}")
            rows = cursor.fetchall()

            header = " | ".join([f"{name:15}" for name in column_names])
            print(header)
            print("-" * len(header))
            for row in rows:
                row_str = " | ".join([f"{str(val)}" for val in row])
                print(row_str)
            if row_count > preview_rows:
                print(f"\n... and {row_count - preview_rows} more rows")
        else:
            print("\nTable is empty.")

        # 导出整表
        print("\nExporting table...")
        csv_path = export_table_to_csv(conn.cursor(), table_name, export_dir)
        jsonl_path = export_table_to_jsonl(conn.cursor(), table_name, export_dir)
        print(f"  -> CSV : {csv_path}")
        print(f"  -> JSON: {jsonl_path}")

    conn.close()
    print(f"\n{'='*60}")
    print("Database inspection completed.")
    print(f"{'='*60}")

if __name__ == "__main__":
    printTable("./database.db")
