# 分布式文件处理系统配置文件

# 数据库模块配置：监听指定目录，解析不同格式文件内容，结构化存储到SQLite数据库
[database]
path = database.db
watch_directories = /home/<USER>/SourceData;/home/<USER>/dataShare
database_enable = true
max_connections = 10
connection_timeout = 30
retry_attempts = 3
retry_delay = 1.0
wal_mode = true
synchronous = NORMAL
cache_size = -64000
temp_store = MEMORY
mmap_size = 268435456
journal_mode = WAL
foreign_keys = true
auto_vacuum = INCREMENTAL
busy_timeout = 30000
insert_sample_data = false

# 文件发布模块配置：监控文件系统事件，通过ZeroMQ发布新文件到指定端点
[file_publish]
watch_paths = /home/<USER>/SourceData;/home/<USER>/dataShare
zmq_publish_addr = tcp://0.0.0.0:13236
topics = leo_data
file_types = *.json
file_publish_enable = true

# API服务模块配置：基于FastAPI提供RESTful接口，支持多客户端查询，结果通过ZeroMQ发布
[api]
host = 0.0.0.0
port = 13231
json_zmq_bind_addr = tcp://0.0.0.0:13239
topics = api_data
api_enable = true

# 订阅服务模块配置：订阅多个ZeroMQ主题，接收并持久化文件到本地
[subscribe]
save_directory = /home/<USER>/dataShare/api_result
zmq_connect_addrs = tcp://*************:13236,tcp://*************:13236,tcp://*************:13236,tcp://*************:13236,tcp://*************:13236,tcp://*************:13236,tcp://*************:13239,tcp://*************:13239
topics = leo_data,api_data
subscribe_enable = true



# 日志配置
[logging]
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO
# 日志格式（注意：INI文件中%需要转义为%%）
format = [%%(asctime)s] [%%(name)s] %%(message)s
# 日志格式（注意：INI文件中%需要转义为%%）
date_format = %%Y-%%m-%%d %%H:%%M:%%S
# log日志保存路径
log_path = /home/<USER>/dataShare.log

# 分布式数据收集和存储模块配置：三线程系统实现数据收集、网络订阅和数据处理
[store]
# 数据库配置
db_path = store.db
max_connections = 10

# 目录监控配置（生产者线程1）
watch_directories = /home/<USER>/SourceData;/home/<USER>/dataShare
zmq_publish_addr = tcp://0.0.0.0:13232

# 网络订阅配置（生产者线程2）
cluster_nodes = 10.1.1.1,10.1.2.1,10.1.3.1,10.1.4.1,10.1.5.1,10.1.6.1,10.1.7.1,10.1.8.1,10.1.9.1,10.1.10.1,10.1.11.1,10.1.12.1,10.1.13.1,10.1.14.1,10.1.15.1,10.1.16.1,10.1.17.1,10.1.18.1,10.1.19.1,10.1.20.1,10.1.21.1,10.1.22.1,10.1.23.1,10.1.24.1,10.1.25.1,10.1.26.1,10.1.27.1,10.1.28.1,10.1.29.1,10.1.30.1,10.1.31.1,10.1.32.1,10.1.33.1,10.1.34.1,10.1.35.1,10.1.36.1,10.1.37.1,10.1.38.1,10.1.39.1,10.1.40.1,10.1.41.1,10.1.42.1,10.1.43.1,10.1.44.1,10.1.45.1,10.1.46.1,10.1.47.1,10.1.48.1,10.1.49.1,10.1.50.1,10.1.51.1,10.1.52.1,10.1.53.1,10.1.54.1,10.1.55.1,10.1.56.1,10.1.57.1,10.1.58.1,10.1.59.1,10.1.60.1,10.1.61.1,10.1.62.1,10.1.63.1,10.1.64.1,10.1.65.1,10.1.66.1,10.1.67.1,10.1.68.1,10.1.69.1,10.1.70.1,10.1.71.1,10.1.72.1,10.1.73.1,10.1.74.1,10.1.75.1,10.1.76.1,10.1.77.1,10.1.78.1,10.1.79.1,10.1.80.1,10.1.81.1,10.1.82.1,10.1.83.1,10.1.84.1,10.1.85.1,10.1.86.1,10.1.87.1,10.1.88.1,10.1.89.1,10.1.90.1,10.1.91.1,10.1.92.1,10.1.93.1,10.1.94.1,10.1.95.1,10.1.96.1,10.1.97.1,10.1.98.1,10.1.99.1,10.1.100.1,10.1.101.1,10.1.102.1,10.1.103.1,10.1.104.1,10.1.105.1,10.1.106.1,10.1.107.1,10.1.108.1,10.1.109.1,10.1.110.1,10.1.111.1,10.1.112.1,10.1.113.1,10.1.114.1,10.1.115.1,10.1.116.1,10.1.117.1,10.1.118.1,10.1.119.1,10.1.120.1,10.1.121.1,10.1.122.1,10.1.123.1,10.1.124.1,10.1.125.1,10.1.126.1,10.1.127.1,10.1.128.1,10.1.129.1,10.1.130.1,10.1.131.1,10.1.132.1,10.1.133.1,10.1.134.1,10.1.135.1,10.1.136.1,10.1.137.1,10.1.138.1,10.1.139.1,10.1.140.1,10.1.141.1,10.1.142.1,10.1.143.1,10.1.144.1,10.1.145.1,10.1.146.1,10.1.147.1,10.1.148.1,10.1.149.1,10.1.150.1,10.1.151.1,10.1.152.1,10.1.153.1,10.1.154.1,10.1.155.1,10.1.156.1,10.1.157.1,10.1.158.1,10.1.159.1,10.1.160.1,10.1.161.1,10.1.162.1,10.1.163.1,10.1.164.1,10.1.165.1,10.1.166.1,10.1.167.1,10.1.168.1,10.1.169.1,10.1.170.1,10.1.171.1,10.1.172.1,10.1.173.1,10.1.174.1,10.1.175.1,10.1.176.1,10.1.177.1,10.1.178.1,10.1.179.1,10.1.180.1,10.1.181.1,10.1.182.1,10.1.183.1,10.1.184.1,10.1.185.1,10.1.186.1,10.1.187.1,10.1.188.1,10.1.189.1,10.1.190.1,10.1.191.1,10.1.192.1,10.1.193.1,10.1.194.1,10.1.195.1,10.1.196.1,10.1.197.1,10.1.198.1,10.1.199.1,10.1.200.1,10.1.201.1,10.1.202.1,10.1.203.1,10.1.204.1,10.1.205.1,10.1.206.1,10.1.207.1,10.1.208.1,10.1.209.1,10.1.210.1,10.1.211.1,10.1.212.1,10.1.213.1,10.1.214.1,10.1.215.1,10.1.216.1,10.1.217.1,10.1.218.1,10.1.219.1,10.1.220.1,10.1.221.1,10.1.222.1,10.1.223.1,10.1.224.1,10.1.225.1,10.1.226.1,10.1.227.1,10.1.228.1,10.1.229.1,10.1.230.1,10.1.231.1,10.1.232.1,10.1.233.1,10.1.234.1,10.1.235.1,10.1.236.1,10.1.237.1,10.1.238.1,10.1.239.1,10.1.240.1,10.1.241.1,10.1.242.1,10.1.243.1,10.1.244.1,10.1.245.1,10.1.246.1,10.1.247.1,10.1.248.1,10.1.249.1,10.1.250.1,10.1.251.1,10.1.252.1,10.1.253.1,10.1.254.1,10.2.0.1,10.2.1.1,10.2.2.1,10.2.3.1,10.2.4.1,10.2.5.1,10.2.6.1,10.2.7.1,10.2.8.1,10.2.9.1,10.2.10.1,10.2.11.1,10.2.12.1,10.2.13.1,10.2.14.1,10.2.15.1,10.2.16.1,10.2.17.1,10.2.18.1,10.2.19.1,10.2.20.1,10.2.21.1,10.2.22.1,10.2.23.1,10.2.24.1,10.2.25.1,10.2.26.1,10.2.27.1,10.2.28.1,10.2.29.1,10.2.30.1,10.2.31.1,1********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********
subscribe_topics = original_data,feature_data

# 数据处理配置（消费者线程）
max_workers = 4
queue_maxsize = 1000

# 服务启用状态
service_enabled = true

# 搜索服务模块配置：基于FastAPI提供RESTful接口，移除ZeroMQ，结果保存到本地文件
[search]
# 服务配置
host = 0.0.0.0
port = 13231

# 文件保存配置
save_directory = /home/<USER>/search_results
file_prefix = search

# 服务启用状态
search_enable = true
